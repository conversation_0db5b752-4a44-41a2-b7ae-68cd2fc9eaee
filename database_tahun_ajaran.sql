-- DATABASE UPDATES UNTUK SISTEM TAHUN AJARAN
-- Script ini menambahkan fitur manajemen tahun ajaran

-- 1. <PERSON><PERSON>hun Ajaran
CREATE TABLE IF NOT EXISTS `tahun_ajaran` (
  `id_tahun_ajaran` int(11) NOT NULL AUTO_INCREMENT,
  `tahun_ajaran` varchar(20) NOT NULL COMMENT 'Format: 2024/2025',
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 = tahun ajaran aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_tahun_ajaran`),
  UNIQUE KEY `unique_tahun_semester` (`tahun_ajaran`, `semester`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert data tahun ajaran
INSERT INTO `tahun_ajaran` (`tahun_ajaran`, `semester`, `tanggal_mulai`, `tanggal_selesai`, `is_active`, `keterangan`) VALUES
('2023/2024', '2', '2024-01-01', '2024-06-30', 0, 'Tahun ajaran lalu - semester 2'),
('2024/2025', '1', '2024-07-01', '2024-12-31', 1, 'Tahun ajaran aktif - semester 1'),
('2024/2025', '2', '2025-01-01', '2025-06-30', 0, 'Tahun ajaran aktif - semester 2');

-- 2. Tabel Riwayat Siswa (untuk tracking naik kelas)
CREATE TABLE IF NOT EXISTS `riwayat_siswa` (
  `id_riwayat` int(11) NOT NULL AUTO_INCREMENT,
  `id_siswa` int(11) NOT NULL,
  `id_tahun_ajaran` int(11) NOT NULL,
  `id_kelas` int(11) NOT NULL,
  `status` enum('aktif','lulus','pindah','keluar') NOT NULL DEFAULT 'aktif',
  `tanggal_masuk` date DEFAULT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_riwayat`),
  KEY `idx_siswa_tahun` (`id_siswa`, `id_tahun_ajaran`),
  FOREIGN KEY (`id_siswa`) REFERENCES `siswa` (`id_siswa`) ON DELETE CASCADE,
  FOREIGN KEY (`id_tahun_ajaran`) REFERENCES `tahun_ajaran` (`id_tahun_ajaran`) ON DELETE CASCADE,
  FOREIGN KEY (`id_kelas`) REFERENCES `kelas` (`id_kelas`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. Update tabel siswa - tambah kolom tahun ajaran
ALTER TABLE `siswa` ADD COLUMN `id_tahun_ajaran_masuk` int(11) DEFAULT NULL COMMENT 'Tahun ajaran pertama masuk';
ALTER TABLE `siswa` ADD COLUMN `status_siswa` enum('aktif','lulus','pindah','keluar') NOT NULL DEFAULT 'aktif';
ALTER TABLE `siswa` ADD COLUMN `tanggal_masuk` date DEFAULT NULL;
ALTER TABLE `siswa` ADD COLUMN `tanggal_lulus` date DEFAULT NULL;

-- 4. Update tabel peminjaman - tambah kolom tahun ajaran
ALTER TABLE `peminjaman` ADD COLUMN `id_tahun_ajaran` int(11) DEFAULT NULL;
ALTER TABLE `peminjaman` ADD INDEX `idx_tahun_ajaran` (`id_tahun_ajaran`);

-- 5. Update tabel pengambilan_atk - tambah kolom tahun ajaran  
ALTER TABLE `pengambilan_atk` ADD COLUMN `id_tahun_ajaran` int(11) DEFAULT NULL;
ALTER TABLE `pengambilan_atk` ADD INDEX `idx_tahun_ajaran` (`id_tahun_ajaran`);

-- 6. Migrasi data existing ke tahun ajaran aktif
-- Set tahun ajaran untuk data yang sudah ada
UPDATE `peminjaman` SET `id_tahun_ajaran` = (SELECT `id_tahun_ajaran` FROM `tahun_ajaran` WHERE `is_active` = 1 LIMIT 1) WHERE `id_tahun_ajaran` IS NULL;
UPDATE `pengambilan_atk` SET `id_tahun_ajaran` = (SELECT `id_tahun_ajaran` FROM `tahun_ajaran` WHERE `is_active` = 1 LIMIT 1) WHERE `id_tahun_ajaran` IS NULL;

-- Set tahun ajaran masuk untuk siswa existing
UPDATE `siswa` SET `id_tahun_ajaran_masuk` = (SELECT `id_tahun_ajaran` FROM `tahun_ajaran` WHERE `is_active` = 1 LIMIT 1) WHERE `id_tahun_ajaran_masuk` IS NULL;

-- Insert riwayat untuk siswa existing
INSERT INTO `riwayat_siswa` (`id_siswa`, `id_tahun_ajaran`, `id_kelas`, `status`, `tanggal_masuk`)
SELECT 
    s.`id_siswa`, 
    (SELECT `id_tahun_ajaran` FROM `tahun_ajaran` WHERE `is_active` = 1 LIMIT 1),
    s.`id_kelas`,
    'aktif',
    CURDATE()
FROM `siswa` s
WHERE NOT EXISTS (
    SELECT 1 FROM `riwayat_siswa` rs 
    WHERE rs.`id_siswa` = s.`id_siswa` 
    AND rs.`id_tahun_ajaran` = (SELECT `id_tahun_ajaran` FROM `tahun_ajaran` WHERE `is_active` = 1 LIMIT 1)
);

-- 7. Tabel Mapping Kelas untuk Naik Kelas Otomatis
CREATE TABLE IF NOT EXISTS `mapping_naik_kelas` (
  `id_mapping` int(11) NOT NULL AUTO_INCREMENT,
  `id_kelas_asal` int(11) NOT NULL,
  `id_kelas_tujuan` int(11) NOT NULL,
  `keterangan` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id_mapping`),
  UNIQUE KEY `unique_mapping` (`id_kelas_asal`, `id_kelas_tujuan`),
  FOREIGN KEY (`id_kelas_asal`) REFERENCES `kelas` (`id_kelas`) ON DELETE CASCADE,
  FOREIGN KEY (`id_kelas_tujuan`) REFERENCES `kelas` (`id_kelas`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert mapping naik kelas (sesuaikan dengan struktur kelas sekolah Anda)
INSERT INTO `mapping_naik_kelas` (`id_kelas_asal`, `id_kelas_tujuan`, `keterangan`) VALUES
-- KPP A -> KPP B -> KPP C -> X-1
(1, 2, 'KPP A naik ke KPP B'),
(2, 3, 'KPP B naik ke KPP C'), 
(3, 4, 'KPP C naik ke X-1'),
-- X-1 -> XI-1, X-2 -> XI-2
(4, 6, 'X-1 naik ke XI-1'),
(5, 7, 'X-2 naik ke XI-2'),
-- XI-1 -> XII-1, XI-2 -> XII-2
(6, 8, 'XI-1 naik ke XII-1'),
(7, 9, 'XI-2 naik ke XII-2'),
-- KPA tetap KPA (kelas khusus)
(10, 10, 'KPA tetap di KPA');

-- 8. View untuk laporan siswa aktif per tahun ajaran
CREATE OR REPLACE VIEW `v_siswa_aktif` AS
SELECT 
    s.`id_siswa`,
    s.`nis`,
    s.`nisn`, 
    s.`nama_siswa`,
    k.`nama_kelas`,
    ta.`tahun_ajaran`,
    ta.`semester`,
    rs.`status`,
    rs.`tanggal_masuk`,
    rs.`tanggal_keluar`
FROM `siswa` s
JOIN `riwayat_siswa` rs ON s.`id_siswa` = rs.`id_siswa`
JOIN `kelas` k ON rs.`id_kelas` = k.`id_kelas`
JOIN `tahun_ajaran` ta ON rs.`id_tahun_ajaran` = ta.`id_tahun_ajaran`
WHERE rs.`status` = 'aktif';

-- 9. View untuk statistik per tahun ajaran
CREATE OR REPLACE VIEW `v_statistik_tahun_ajaran` AS
SELECT 
    ta.`tahun_ajaran`,
    ta.`semester`,
    COUNT(DISTINCT rs.`id_siswa`) as `total_siswa`,
    COUNT(DISTINCT p.`id_peminjaman`) as `total_peminjaman`,
    COUNT(DISTINCT pa.`id_pengambilan_atk`) as `total_pengambilan_atk`
FROM `tahun_ajaran` ta
LEFT JOIN `riwayat_siswa` rs ON ta.`id_tahun_ajaran` = rs.`id_tahun_ajaran` AND rs.`status` = 'aktif'
LEFT JOIN `peminjaman` p ON ta.`id_tahun_ajaran` = p.`id_tahun_ajaran`
LEFT JOIN `pengambilan_atk` pa ON ta.`id_tahun_ajaran` = pa.`id_tahun_ajaran`
GROUP BY ta.`id_tahun_ajaran`, ta.`tahun_ajaran`, ta.`semester`
ORDER BY ta.`tahun_ajaran` DESC, ta.`semester` DESC;
