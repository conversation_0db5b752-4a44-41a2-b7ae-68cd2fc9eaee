[{"name": "dompdf/dompdf", "version": "v0.8.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "75f13c700009be21a1965dc2c5b68a8708c22ba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/75f13c700009be21a1965dc2c5b68a8708c22ba2", "reference": "75f13c700009be21a1965dc2c5b68a8708c22ba2", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "0.5.*", "phenx/php-svg-lib": "0.3.*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance"}, "time": "2018-12-14T02:40:31+00:00", "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf"}, {"name": "phenx/php-font-lib", "version": "0.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PhenX/php-font-lib.git", "reference": "760148820110a1ae0936e5cc35851e25a938bc97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-font-lib/zipball/760148820110a1ae0936e5cc35851e25a938bc97", "reference": "760148820110a1ae0936e5cc35851e25a938bc97", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^4.8"}, "time": "2017-09-13T16:14:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib"}, {"name": "phenx/php-svg-lib", "version": "v0.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "shasum": ""}, "require": {"sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^5.5|^6.5"}, "time": "2019-09-11T20:02:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib"}, {"name": "sabberworm/php-css-parser", "version": "8.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f", "reference": "91bcc3e3fdb7386c9a2e0e0aa09ca75cc43f121f", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "~4.8"}, "time": "2019-02-22T07:42:52+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Sabberworm\\CSS": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "http://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"]}]