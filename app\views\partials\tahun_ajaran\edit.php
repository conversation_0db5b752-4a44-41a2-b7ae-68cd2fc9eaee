<?php 
$page_id = null;
$comp_model = new SharedController;
$current_page = $this->set_current_page_link();
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0"><i class="fa fa-edit"></i> Edit Tahun Ajaran</h4>
                </div>
                <div class="card-body">
                    <?php $this->display_page_errors(); ?>
                    
                    <form method="post" action="<?php print_link('tahun_ajaran/edit/' . $data['id_tahun_ajaran'] . '?csrf_token=' . Csrf::$token); ?>">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tahun_ajaran">Tahun Ajaran <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="tahun_ajaran" name="tahun_ajaran" 
                                           placeholder="Contoh: 2025/2026" required
                                           value="<?php echo get_form_field_value('tahun_ajaran', $data['tahun_ajaran']); ?>">
                                    <small class="form-text text-muted">Format: YYYY/YYYY</small>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="semester">Semester <span class="text-danger">*</span></label>
                                    <select class="form-control" id="semester" name="semester" required>
                                        <option value="">Pilih Semester</option>
                                        <option value="1" <?php echo get_form_field_value('semester', $data['semester']) == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                        <option value="2" <?php echo get_form_field_value('semester', $data['semester']) == '2' ? 'selected' : ''; ?>>Semester 2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tanggal_mulai">Tanggal Mulai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="tanggal_mulai" name="tanggal_mulai" 
                                           required value="<?php echo get_form_field_value('tanggal_mulai', $data['tanggal_mulai']); ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tanggal_selesai">Tanggal Selesai <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="tanggal_selesai" name="tanggal_selesai" 
                                           required value="<?php echo get_form_field_value('tanggal_selesai', $data['tanggal_selesai']); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1"
                                       <?php echo get_form_field_value('is_active', $data['is_active']) == '1' ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_active">
                                    <strong>Set sebagai tahun ajaran aktif</strong>
                                </label>
                                <small class="form-text text-muted">Jika dicentang, tahun ajaran ini akan menjadi aktif dan tahun ajaran lain akan dinonaktifkan</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="keterangan">Keterangan</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                                      placeholder="Keterangan tambahan (opsional)"><?php echo get_form_field_value('keterangan', $data['keterangan']); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-warning">
                                <i class="fa fa-save"></i> Update Tahun Ajaran
                            </button>
                            <a href="<?php print_link('tahun_ajaran/view/' . $data['id_tahun_ajaran']); ?>" class="btn btn-info">
                                <i class="fa fa-eye"></i> Lihat Detail
                            </a>
                            <a href="<?php print_link('tahun_ajaran'); ?>" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tanggalMulaiInput = document.getElementById('tanggal_mulai');
    const tanggalSelesaiInput = document.getElementById('tanggal_selesai');
    const semesterSelect = document.getElementById('semester');
    const tahunAjaranInput = document.getElementById('tahun_ajaran');
    
    // Auto-set tanggal berdasarkan semester
    semesterSelect.addEventListener('change', function() {
        const tahunAjaran = tahunAjaranInput.value;
        if (tahunAjaran && tahunAjaran.includes('/')) {
            const years = tahunAjaran.split('/');
            const year1 = years[0];
            const year2 = years[1];
            
            if (this.value === '1') {
                // Semester 1: Juli - Desember
                tanggalMulaiInput.value = year1 + '-07-01';
                tanggalSelesaiInput.value = year1 + '-12-31';
            } else if (this.value === '2') {
                // Semester 2: Januari - Juni
                tanggalMulaiInput.value = year2 + '-01-01';
                tanggalSelesaiInput.value = year2 + '-06-30';
            }
        }
    });
    
    // Validasi tanggal
    tanggalSelesaiInput.addEventListener('change', function() {
        const tanggalMulai = new Date(tanggalMulaiInput.value);
        const tanggalSelesai = new Date(this.value);
        
        if (tanggalSelesai <= tanggalMulai) {
            alert('Tanggal selesai harus lebih besar dari tanggal mulai');
            this.value = '';
        }
    });
});
</script>
