-- Database Updates untuk Aplikasi Peminjaman (TANPA SISTEM LOGIN)
-- Jalankan script ini untuk menambahkan tabel dan perbaikan yang diperlukan

-- 1. Tabel Master Barang
CREATE TABLE IF NOT EXISTS `barang` (
  `id_barang` int(11) NOT NULL AUTO_INCREMENT,
  `kode_barang` varchar(20) NOT NULL UNIQUE,
  `nama_barang` varchar(100) NOT NULL,
  `kategori` enum('elektronik','kunci','sound','proyektor','lainnya') NOT NULL DEFAULT 'lainnya',
  `kondisi` enum('baik','rusak','hilang') NOT NULL DEFAULT 'baik',
  `lokasi` varchar(100) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_barang`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert data barang berdasarkan data peminjaman yang ada
INSERT INTO `barang` (`kode_barang`, `nama_barang`, `kategori`, `lokasi`) VALUES
('KEY001', 'Kunci Lab Bahasa', 'kunci', 'Lab Bahasa'),
('KEY002', 'Kunci Ruang Rapat Guru', 'kunci', 'Ruang Rapat Guru'),
('KEY003', 'Kunci Lab Multimedia', 'kunci', 'Lab Multimedia'),
('KEY004', 'Kunci Lab Sosial', 'kunci', 'Lab Sosial'),
('SND001', 'Sound Woofer', 'sound', 'Gudang Audio'),
('SND002', 'Sound Kecil', 'sound', 'Gudang Audio'),
('PRJ001', 'Proyektor', 'proyektor', 'Ruang Media');

-- 2. Tabel Master ATK
CREATE TABLE IF NOT EXISTS `atk` (
  `id_atk` int(11) NOT NULL AUTO_INCREMENT,
  `nama_atk` varchar(100) NOT NULL,
  `satuan` varchar(20) NOT NULL DEFAULT 'pcs',
  `stok` int(11) NOT NULL DEFAULT 0,
  `stok_minimum` int(11) NOT NULL DEFAULT 5,
  `harga_satuan` decimal(10,2) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_atk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert data ATK berdasarkan data pengambilan yang ada
INSERT INTO `atk` (`nama_atk`, `satuan`, `stok`, `stok_minimum`) VALUES
('Spidol', 'pcs', 100, 10),
('Reffil Spidol', 'pcs', 50, 5),
('Reffil Tinta', 'pcs', 75, 8);

-- 3. Tabel Log Aktivitas (Opsional - untuk tracking tanpa user)
CREATE TABLE IF NOT EXISTS `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity` varchar(255) NOT NULL,
  `table_name` varchar(50) NOT NULL,
  `record_id` int(11) NOT NULL,
  `old_data` text DEFAULT NULL,
  `new_data` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 4. Perbaikan Foreign Key untuk tabel yang ada
-- Ubah tipe data untuk konsistensi
ALTER TABLE `siswa` MODIFY `id_kelas` int(11) NOT NULL;
ALTER TABLE `peminjaman` MODIFY `nama` int(11) NOT NULL COMMENT 'ID Siswa';
ALTER TABLE `peminjaman` MODIFY `kelas` int(11) NOT NULL COMMENT 'ID Kelas';
ALTER TABLE `pengambilan_atk` MODIFY `nama` int(11) NOT NULL COMMENT 'ID Siswa';
ALTER TABLE `pengambilan_atk` MODIFY `kelas` int(11) NOT NULL COMMENT 'ID Kelas';

-- Tambah foreign key constraints
ALTER TABLE `siswa` ADD CONSTRAINT `fk_siswa_kelas` FOREIGN KEY (`id_kelas`) REFERENCES `kelas` (`id_kelas`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `peminjaman` ADD CONSTRAINT `fk_peminjaman_siswa` FOREIGN KEY (`nama`) REFERENCES `siswa` (`id_siswa`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `peminjaman` ADD CONSTRAINT `fk_peminjaman_kelas` FOREIGN KEY (`kelas`) REFERENCES `kelas` (`id_kelas`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `pengambilan_atk` ADD CONSTRAINT `fk_pengambilan_siswa` FOREIGN KEY (`nama`) REFERENCES `siswa` (`id_siswa`) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE `pengambilan_atk` ADD CONSTRAINT `fk_pengambilan_kelas` FOREIGN KEY (`kelas`) REFERENCES `kelas` (`id_kelas`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 5. Tambah kolom untuk tracking
ALTER TABLE `peminjaman` ADD COLUMN `status` enum('dipinjam','dikembalikan','terlambat') NOT NULL DEFAULT 'dipinjam';
ALTER TABLE `peminjaman` ADD COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `peminjaman` ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

ALTER TABLE `pengambilan_atk` ADD COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `pengambilan_atk` ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update status peminjaman berdasarkan tanggal kembali
UPDATE `peminjaman` SET `status` = 'dikembalikan' WHERE `tanggal_kembali` != '0000-00-00' AND `tanggal_kembali` <= CURDATE();
UPDATE `peminjaman` SET `status` = 'terlambat' WHERE `tanggal_kembali` != '0000-00-00' AND `tanggal_kembali` < CURDATE() AND `status` = 'dipinjam';
