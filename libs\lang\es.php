<?php

return array(
    'validate_required'                 => 'El campo {field} es requerido',
    'validate_valid_email'              => 'El campo {field} debe ser una dirección de correo electrónico válida',
    'validate_max_len'                  => 'El campo {field} no puede tener más de {param} caracteres de longitud',
    'validate_min_len'                  => 'El campo {field} debe tener al menos {param} caracteres de longitud',
    'validate_exact_len'                => 'El campo {field} debe tener {param} caracteres de longitud',
    'validate_alpha'                    => 'El campo {field} sólo puede contener letras',
    'validate_alpha_numeric'            => 'El campo {field} sólo puede contener letras y números',
    'validate_alpha_numeric_space'      => 'El campo {field} solo puede contener letras, números y espacios',
    'validate_alpha_dash'               => 'El campo {field} sólo puede contener letras y guiones',
    'validate_alpha_space'              => 'El campo {field} sólo puede contener letras y espacios',
    'validate_numeric'                  => 'El campo {field} sólo puede contener caracteres numéricos',
    'validate_integer'                  => 'El campo {field} sólo puede contener un valor numérico',
    'validate_boolean'                  => 'El campo {field} debe ser verdadero o falso',
    'validate_float'                    => 'El campo {field} sólo puede contener un valor flotante',
    'validate_valid_url'                => 'El campo {field} debe ser una dirección URL válida',
    'validate_url_exists'               => 'El campo {field} debe ser una dirección URL existente',
    'validate_valid_ip'                 => 'El campo {field} debe contener una dirección IP válida',
    'validate_valid_ipv4'               => 'El campo {field} debe contener una dirección IPv4 válida',
    'validate_valid_ipv6'               => 'El campo {field} debe contener una dirección IPv6 válida',
    'validate_guidv4'                   => 'El campo {field} debe contener un GUID válido',
    'validate_valid_cc'                 => 'El campo {field} debe contener un número de tarjeta de crédito válido',
    'validate_valid_name'               => 'El campo {field} debe contener un nombre humano válido',
    'validate_contains'                 => 'El campo {field} debe contener uno de los siguientes valores: {param}',
    'validate_contains_list'            => 'El campo {field} debe contener un valor de su lista desplegable',
    'validate_doesnt_contain_list'      => 'El campo {field} contiene un valor que no es aceptado',
    'validate_street_address'           => 'El campo {field} debe contener una dirección válida',
    'validate_date'                     => 'El campo {field} debe ser una fecha válida',
    'validate_min_numeric'              => 'El campo {field} debe ser un valor numérico mayor o igual que {param}',
    'validate_max_numeric'              => 'El campo {field} debe ser un valor numérico menor o igual que {param}',
    'validate_min_age'                  => 'El campo {field} debe tener una edad mayor o igual que {param}',
    'validate_invalid'                  => 'El campo {field} es inválido',
    'validate_starts'                   => 'El campo {field} debe comenzar con {param}',
    'validate_extension'                => 'El campo {field} puede contener una de las siguientes extensiones {param}',
    'validate_required_file'            => 'El campo {field} es requerido',
    'validate_equalsfield'              => 'El campo {field} no equivale al campo {param}',
    'validate_iban'                     => 'El campo {field} debe contener un IBAN válido',
    'validate_phone_number'             => 'El campo {field} debe contener un número de teléfono válido',
    'validate_regex'                    => 'El campo {field} debe contener un valor válido',
    'validate_valid_json_string'        => 'El campo {field} debe contener una cadena con el formato JSON válido',
    'validate_valid_array_size_greater' => 'El campo {field} debe ser un arreglo con el tamaño, igual o mayor que {param}',
    'validate_valid_array_size_lesser'  => 'El campo {field} debe ser un arreglo con el tamaño, igual o menor que {param}',
    'validate_valid_array_size_equal'   => 'El campo {field} debe ser un arreglo con el tamaño igual a {param}',
);
