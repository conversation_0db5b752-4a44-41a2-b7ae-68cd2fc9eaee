-- SCRIPT UNTUK MEMPERBAIKI ERROR DUPLICATE ENTRY
-- Jalankan script ini untuk mengatasi error yang sudah terjadi

-- 1. Drop tabel yang bermasalah (jika ada)
DROP TABLE IF EXISTS `tahun_ajaran`;

-- 2. Buat ulang tabel dengan constraint yang benar
CREATE TABLE `tahun_ajaran` (
  `id_tahun_ajaran` int(11) NOT NULL AUTO_INCREMENT,
  `tahun_ajaran` varchar(20) NOT NULL COMMENT 'Format: 2024/2025',
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 = tahun ajaran aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_tahun_ajaran`),
  UNIQUE KEY `unique_tahun_semester` (`tahun_ajaran`, `semester`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 3. Insert data contoh yang benar
INSERT INTO `tahun_ajaran` (`tahun_ajaran`, `semester`, `tanggal_mulai`, `tanggal_selesai`, `is_active`, `keterangan`) VALUES
('2023/2024', '2', '2024-01-01', '2024-06-30', 0, 'Tahun ajaran lalu - semester 2'),
('2024/2025', '1', '2024-07-01', '2024-12-31', 1, 'Tahun ajaran aktif - semester 1'),
('2024/2025', '2', '2025-01-01', '2025-06-30', 0, 'Tahun ajaran aktif - semester 2 (belum dimulai)');

-- 4. Verifikasi data
SELECT 'Tabel tahun_ajaran berhasil diperbaiki!' as status;
SELECT * FROM tahun_ajaran ORDER BY tahun_ajaran, semester;
