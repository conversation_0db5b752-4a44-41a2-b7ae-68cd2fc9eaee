<?php

return array(
    'validate_required'                 => 'O preenchimento do campo {field} é obrigatório',
    'validate_valid_email'              => 'O campo {field} precisa conter um e-mail válido',
    'validate_max_len'                  => 'O campo {field} pode conter no máximo {param} caracteres',
    'validate_min_len'                  => 'O campo {field} precisa conter no mínimo {param} caracteres',
    'validate_exact_len'                => 'O campo {field} precisa conter exatamente {param} caracteres',
    'validate_alpha'                    => 'O campo {field} pode conter apenas letras',
    'validate_alpha_numeric'            => 'O campo {field} pode conter apenas letras e números',
    'validate_alpha_numeric_space'      => 'O campo {field} pode conter apenas letras, números e espaços',
    'validate_alpha_dash'               => 'O campo {field} pode conter apenas letras e traços',
    'validate_alpha_space'              => 'O campo {field} pode conter apenas letras e espaços',
    'validate_numeric'                  => 'O campo {field} precisa ser um número',
    'validate_integer'                  => 'O campo {field} precisa ser um número inteiro, sem decimal',
    'validate_boolean'                  => 'O campo {field} deve ser verdadeiro ou falso',
    'validate_float'                    => 'O campo {field} precisa ser um número com (float) casas decimais',
    'validate_valid_url'                => 'O campo {field} precisa ser uma url válida',
    'validate_url_exists'               => 'O campo {field} possui uma url que não existe',
    'validate_valid_ip'                 => 'O campo {field} precisa conter um IP válido',
    'validate_valid_ipv4'               => 'O campo {field} precisa conter um endereço de IPv4 válido',
    'validate_valid_ipv6'               => 'O campo {field} precisa conter um endereço de IPv6 válido',
    'validate_guidv4'                   => 'O campo {field} precisa conter um valor válido de GUID',
    'validate_valid_cc'                 => 'O campo {field} não possui um valor de cartão de crédito válido',
    'validate_valid_name'               => 'O campo {field} precisa conter um nome completo',
    'validate_contains'                 => 'O campo {field} pode conter apenas um dos valores a seguir: {param}',
    'validate_contains_list'            => 'O campo {field} foi preenchido com uma opção inválida',
    'validate_doesnt_contain_list'      => 'O campo {field} contém um valor que não é aceito',
    'validate_street_address'           => 'O campo {field} precisa conter um nome de rua válido',
    'validate_date'                     => 'O campo {field} precisa ser uma data válida',
    'validate_min_numeric'              => 'O campo {field} precisa conter um valor numérico, igual, ou maior que {param}',
    'validate_max_numeric'              => 'O campo {field} precisa conter um valor numérico, igual, ou menor que {param}',
    'validate_min_age'                  => 'O campo {field} precisa conter uma idade maior ou igual a {param}',
    'validate_invalid'                  => 'O campo {field} é inválido',
    'validate_starts'                   => 'O campo {field} precisa iniciar com {param}',
    'validate_extension'                => 'O campo {field} permite apenas os seguintes formatos: {param}',
    'validate_required_file'            => 'O campo {field} é de preenchimento obrigatório',
    'validate_equalsfield'              => 'O campo {field} não é igual ao campo {param}',
    'validate_iban'                     => 'O campo {field} precisa conter um número IBAN válido',
    'validate_phone_number'             => 'O campo {field} precisa conter um número de telefone válido',
    'validate_regex'                    => 'O campo {field} precisa conter um valor com formato válido',
    'validate_valid_json_string'        => 'O campo {field} precisa conter um string com formato JSON',
    'validate_valid_array_size_greater' => 'O campo {field} precisa conter um array com tamanho, igual, ou maior que {param}',
    'validate_valid_array_size_lesser'  => 'O campo {field} precisa conter um array com tamanho, igual, ou menor que {param}',
    'validate_valid_array_size_equal'   => 'O campo {field} precisa conter um array com tamanho igual a {param}',
);
