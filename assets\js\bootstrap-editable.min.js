/*! X-editable - v1.5.3 
* In-place editing with Twitter Bootstrap, jQuery UI or pure jQuery
* http://github.com/vitalets/x-editable
* Copyright (c) 2018 <PERSON>liy Potapov; Licensed MIT */

/*! This libary has been greatly modified to for phprad application 

* New editable components  were included such as:
	Selectize
	FlatPickr DateTime Picker
	RadioList components
	
* Components not used by PHPRad were removed:
	Select2
	Bootstrap Datetime Picker
**/

!function(t){"use strict";var e=function(e,i){this.options=t.extend({},t.fn.editableform.defaults,i),this.$div=t(e),this.options.scope||(this.options.scope=this)};e.prototype={constructor:e,initInput:function(){this.input=this.options.input,this.value=this.input.str2value(this.options.value),this.input.prerender()},initTemplate:function(){this.$form=t(t.fn.editableform.template)},initButtons:function(){var e=this.$form.find(".editable-buttons");e.append(t.fn.editableform.buttons),"bottom"===this.options.showbuttons&&e.addClass("editable-buttons-bottom")},render:function(){this.$loading=t(t.fn.editableform.loading),this.$div.empty().append(this.$loading),this.initTemplate(),this.options.showbuttons?this.initButtons():this.$form.find(".editable-buttons").remove(),this.showLoading(),this.isSaving=!1,this.$div.triggerHandler("rendering"),this.initInput(),this.$form.find("div.editable-input").append(this.input.$tpl),this.$div.append(this.$form),t.when(this.input.render()).then(t.proxy(function(){if(this.options.showbuttons||this.input.autosubmit(),this.$form.find(".editable-cancel").click(t.proxy(this.cancel,this)),this.input.error)this.error(this.input.error),this.$form.find(".editable-submit").attr("disabled",!0),this.input.$input.attr("disabled",!0),this.$form.submit(function(t){t.preventDefault()});else{this.error(!1),this.input.$input.removeAttr("disabled"),this.$form.find(".editable-submit").removeAttr("disabled");var e=null===this.value||void 0===this.value||""===this.value?this.options.defaultValue:this.value;this.input.value2input(e),this.$form.submit(t.proxy(this.submit,this))}this.$div.triggerHandler("rendered"),this.showForm(),this.input.postrender&&this.input.postrender()},this))},cancel:function(){this.$div.triggerHandler("cancel")},showLoading:function(){var t,e;this.$form?(t=this.$form.outerWidth(),e=this.$form.outerHeight(),t&&this.$loading.width(t),e&&this.$loading.height(e),this.$form.hide()):(t=this.$loading.parent().width())&&this.$loading.width(t),this.$loading.show()},showForm:function(t){this.$loading.hide(),this.$form.show(),!1!==t&&this.input.activate(),this.$div.triggerHandler("show")},error:function(e){var i,n=this.$form.find(".control-group"),s=this.$form.find(".editable-error-block");if(!1===e)n.removeClass(t.fn.editableform.errorGroupClass),s.removeClass(t.fn.editableform.errorBlockClass).empty().hide();else{if(e){i=(""+e).split("\n");for(var o=0;o<i.length;o++)i[o]=t("<div>").text(i[o]).html();e=i.join("<br>")}n.addClass(t.fn.editableform.errorGroupClass),s.addClass(t.fn.editableform.errorBlockClass).html(e).show()}},submit:function(e){e.stopPropagation(),e.preventDefault();var i=this.input.input2value(),n=this.validate(i);if("object"===t.type(n)&&void 0!==n.newValue){if(i=n.newValue,this.input.value2input(i),"string"==typeof n.msg)return this.error(n.msg),void this.showForm()}else if(n)return this.error(n),void this.showForm();if(this.options.savenochange||this.input.value2str(i)!==this.input.value2str(this.value)){var s=this.input.value2submit(i);this.isSaving=!0,t.when(this.save(s)).done(t.proxy(function(t){this.isSaving=!1;var e="function"==typeof this.options.success?this.options.success.call(this.options.scope,t,i):null;return!1===e?(this.error(!1),void this.showForm(!1)):"string"==typeof e?(this.error(e),void this.showForm()):(e&&"object"==typeof e&&e.hasOwnProperty("newValue")&&(i=e.newValue),this.error(!1),this.value=i,void this.$div.triggerHandler("save",{newValue:i,submitValue:s,response:t}))},this)).fail(t.proxy(function(t){var e;this.isSaving=!1,e="function"==typeof this.options.error?this.options.error.call(this.options.scope,t,i):"string"==typeof t?t:t.responseText||t.statusText||"Unknown error!",this.error(e),this.showForm()},this))}else this.$div.triggerHandler("nochange")},save:function(e){this.options.pk=t.fn.editableutils.tryParseJson(this.options.pk,!0);var i,n="function"==typeof this.options.pk?this.options.pk.call(this.options.scope):this.options.pk;if(!!("function"==typeof this.options.url||this.options.url&&("always"===this.options.send||"auto"===this.options.send&&null!=n)))return this.showLoading(),i={name:this.options.name||"",value:e,pk:n},"function"==typeof this.options.params?i=this.options.params.call(this.options.scope,i):(this.options.params=t.fn.editableutils.tryParseJson(this.options.params,!0),t.extend(i,this.options.params)),"function"==typeof this.options.url?this.options.url.call(this.options.scope,i):t.ajax(t.extend({url:this.options.url,data:i,type:"POST"},this.options.ajaxOptions))},validate:function(t){if(void 0===t&&(t=this.value),"function"==typeof this.options.validate)return this.options.validate.call(this.options.scope,t)},option:function(t,e){t in this.options&&(this.options[t]=e),"value"===t&&this.setValue(e)},setValue:function(t,e){this.value=e?this.input.str2value(t):t,this.$form&&this.$form.is(":visible")&&this.input.value2input(this.value)}},t.fn.editableform=function(i){var n=arguments;return this.each(function(){var s=t(this),o=s.data("editableform"),a="object"==typeof i&&i;o||s.data("editableform",o=new e(this,a)),"string"==typeof i&&o[i].apply(o,Array.prototype.slice.call(n,1))})},t.fn.editableform.Constructor=e,t.fn.editableform.defaults={type:"text",url:null,params:null,name:null,pk:null,value:null,defaultValue:null,send:"auto",validate:null,success:null,error:null,ajaxOptions:null,showbuttons:!0,scope:null,savenochange:!1},t.fn.editableform.template='<form class="form editableform"><div class="control-group"><div><div class="editable-input"></div><div class="editable-buttons"></div></div><div class="editable-error-block"></div></div></form>',t.fn.editableform.loading='<div class="editableform-loading"></div>',t.fn.editableform.buttons='<button type="submit" class="editable-submit">ok</button><button type="button" class="editable-cancel">cancel</button>',t.fn.editableform.errorGroupClass=null,t.fn.editableform.errorBlockClass="editable-error",t.fn.editableform.engine="jquery"}(window.jQuery),function(t){"use strict";t.fn.editableutils={inherit:function(t,e){var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype},setCursorPosition:function(t,e){if(t.setSelectionRange)try{t.setSelectionRange(e,e)}catch(t){}else if(t.createTextRange){var i=t.createTextRange();i.collapse(!0),i.moveEnd("character",e),i.moveStart("character",e),i.select()}},tryParseJson:function(t,e){if("string"==typeof t&&t.length&&t.match(/^[\{\[].*[\}\]]$/))if(e)try{t=new Function("return "+t)()}catch(t){}finally{return t}else t=new Function("return "+t)();return t},sliceObj:function(e,i,n){var s,o,a={};if(!t.isArray(i)||!i.length)return a;for(var l=0;l<i.length;l++)s=i[l],e.hasOwnProperty(s)&&(a[s]=e[s]),!0!==n&&(o=s.toLowerCase(),e.hasOwnProperty(o)&&(a[s]=e[o]));return a},getConfigData:function(e){var i={};return t.each(e[0].dataset,function(t,e){("object"!=typeof e||e&&"object"==typeof e&&(e.constructor===Object||e.constructor===Array))&&(i[t]=e)}),i},objectKeys:function(t){if(Object.keys)return Object.keys(t);if(t!==Object(t))throw new TypeError("Object.keys called on a non-object");var e,i=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&i.push(e);return i},escape:function(e){return t("<div>").text(e).html()},itemsByValue:function(e,i,n){if(!i||null===e)return[];if("function"!=typeof n){var s=n||"value";n=function(t){return t[s]}}var o=t.isArray(e),a=[],l=this;return t.each(i,function(i,s){if(s.children)a=a.concat(l.itemsByValue(e,s.children,n));else if(o)t.grep(e,function(t){return t==(s&&"object"==typeof s?n(s):s)}).length&&a.push(s);else{var r=s&&"object"==typeof s?n(s):s;e==r&&a.push(s)}}),a},createInput:function(e){var i,n=e.type;return"date"!==n&&"datetime"!==n||(n="flatdatetimepicker"),"wysihtml5"!==n||t.fn.editabletypes[n]||(n="textarea"),"function"==typeof t.fn.editabletypes[n]?new(i=t.fn.editabletypes[n])(this.sliceObj(e,this.objectKeys(i.defaults))):(t.error("Unknown type: "+n),!1)},supportsTransitions:function(){var t=(document.body||document.documentElement).style,e="transition",i=["Moz","Webkit","Khtml","O","ms"];if("string"==typeof t[e])return!0;e=e.charAt(0).toUpperCase()+e.substr(1);for(var n=0;n<i.length;n++)if("string"==typeof t[i[n]+e])return!0;return!1}}}(window.jQuery),function(t){"use strict";var e=function(t,e){this.init(t,e)},i=function(t,e){this.init(t,e)};e.prototype={containerName:null,containerDataName:null,innerCss:null,containerClass:"editable-container editable-popup",defaults:{},init:function(i,n){this.$element=t(i),this.options=t.extend({},t.fn.editableContainer.defaults,n),this.splitOptions(),this.formOptions.scope=this.$element[0],this.initContainer(),this.delayedHide=!1,this.$element.on("destroyed",t.proxy(function(){this.destroy()},this)),t(document).data("editable-handlers-attached")||(t(document).on("keyup.editable",function(e){27===e.which&&t(".editable-open").editableContainer("hide","cancel")}),t(document).on("click.editable",function(i){var n,s=t(i.target),o=[".editable-container",".ui-datepicker-header",".datepicker",".modal-backdrop",".bootstrap-wysihtml5-insert-image-modal",".bootstrap-wysihtml5-insert-link-modal"];if(!t(".select2-drop-mask").is(":visible")&&t.contains(document.documentElement,i.target)&&!s.is(document)){for(n=0;n<o.length;n++)if(s.is(o[n])||s.parents(o[n]).length)return;e.prototype.closeOthers(i.target)}}),t(document).data("editable-handlers-attached",!0))},splitOptions:function(){if(this.containerOptions={},this.formOptions={},!t.fn[this.containerName])throw new Error(this.containerName+" not found. Have you included corresponding js file?");for(var e in this.options)e in this.defaults?this.containerOptions[e]=this.options[e]:this.formOptions[e]=this.options[e]},tip:function(){return this.container()?this.container().$tip:null},container:function(){var t;return this.containerDataName&&(t=this.$element.data(this.containerDataName))?t:t=this.$element.data(this.containerName)},call:function(){this.$element[this.containerName].apply(this.$element,arguments)},initContainer:function(){this.call(this.containerOptions)},renderForm:function(){this.$form.editableform(this.formOptions).on({save:t.proxy(this.save,this),nochange:t.proxy(function(){this.hide("nochange")},this),cancel:t.proxy(function(){this.hide("cancel")},this),show:t.proxy(function(){this.delayedHide?(this.hide(this.delayedHide.reason),this.delayedHide=!1):this.setPosition()},this),rendering:t.proxy(this.setPosition,this),resize:t.proxy(this.setPosition,this),rendered:t.proxy(function(){this.$element.triggerHandler("shown",t(this.options.scope).data("editable"))},this)}).editableform("render")},show:function(e){this.$element.addClass("editable-open"),!1!==e&&this.closeOthers(this.$element[0]),this.innerShow(),this.tip().addClass(this.containerClass),this.$form,this.$form=t("<div>"),this.tip().is(this.innerCss)?this.tip().append(this.$form):this.tip().find(this.innerCss).append(this.$form),this.renderForm()},hide:function(t){this.tip()&&this.tip().is(":visible")&&this.$element.hasClass("editable-open")&&(this.$form.data("editableform").isSaving?this.delayedHide={reason:t}:(this.delayedHide=!1,this.$element.removeClass("editable-open"),this.innerHide(),this.$element.triggerHandler("hidden",t||"manual")))},innerShow:function(){},innerHide:function(){},toggle:function(t){this.container()&&this.tip()&&this.tip().is(":visible")?this.hide():this.show(t)},setPosition:function(){},save:function(t,e){this.$element.triggerHandler("save",e),this.hide("save")},option:function(t,e){this.options[t]=e,t in this.containerOptions?(this.containerOptions[t]=e,this.setContainerOption(t,e)):(this.formOptions[t]=e,this.$form&&this.$form.editableform("option",t,e))},setContainerOption:function(t,e){this.call("option",t,e)},destroy:function(){this.hide(),this.innerDestroy(),this.$element.off("destroyed"),this.$element.removeData("editableContainer")},innerDestroy:function(){},closeOthers:function(e){t(".editable-open").each(function(i,n){if(n!==e&&!t(n).find(e).length){var s=t(n),o=s.data("editableContainer");o&&("cancel"===o.options.onblur?s.data("editableContainer").hide("onblur"):"submit"===o.options.onblur&&s.data("editableContainer").tip().find("form").submit())}})},activate:function(){this.tip&&this.tip().is(":visible")&&this.$form&&this.$form.data("editableform").input.activate()}},t.fn.editableContainer=function(n){var s=arguments;return this.each(function(){var o=t(this),a=o.data("editableContainer"),l="object"==typeof n&&n,r="inline"===l.mode?i:e;a||o.data("editableContainer",a=new r(this,l)),"string"==typeof n&&a[n].apply(a,Array.prototype.slice.call(s,1))})},t.fn.editableContainer.Popup=e,t.fn.editableContainer.Inline=i,t.fn.editableContainer.defaults={value:null,placement:"top",autohide:!0,onblur:"cancel",anim:!1,mode:"popup"},jQuery.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableContainer.Inline.prototype,t.fn.editableContainer.Popup.prototype,{containerName:"editableform",innerCss:".editable-inline",containerClass:"editable-container editable-inline",initContainer:function(){this.$tip=t("<span></span>"),this.options.anim||(this.options.anim=0)},splitOptions:function(){this.containerOptions={},this.formOptions=this.options},tip:function(){return this.$tip},innerShow:function(){this.$element.hide(),this.tip().insertAfter(this.$element).show()},innerHide:function(){this.$tip.hide(this.options.anim,t.proxy(function(){this.$element.show(),this.innerDestroy()},this))},innerDestroy:function(){this.tip()&&this.tip().empty().remove()}})}(window.jQuery),function(t){"use strict";var e=function(e,i){this.$element=t(e),this.options=t.extend({},t.fn.editable.defaults,i,t.fn.editableutils.getConfigData(this.$element)),this.options.selector?this.initLive():this.init(),this.options.highlight&&!t.fn.editableutils.supportsTransitions()&&(this.options.highlight=!1)};e.prototype={constructor:e,init:function(){var e,i=!1;if(this.options.name=this.options.name||this.$element.attr("id"),this.options.scope=this.$element[0],this.input=t.fn.editableutils.createInput(this.options),this.input){switch(void 0===this.options.value||null===this.options.value?(this.value=this.input.html2value(t.trim(this.$element.html())),i=!0):(this.options.value=t.fn.editableutils.tryParseJson(this.options.value,!0),"string"==typeof this.options.value?this.value=this.input.str2value(this.options.value):this.value=this.options.value),this.$element.addClass("editable"),"textarea"===this.input.type&&this.$element.addClass("editable-pre-wrapped"),"manual"!==this.options.toggle?(this.$element.addClass("editable-click"),this.$element.on(this.options.toggle+".editable",t.proxy(function(t){if(this.options.disabled||t.preventDefault(),"mouseenter"===this.options.toggle)this.show();else{var e="click"!==this.options.toggle;this.toggle(e)}},this))):this.$element.attr("tabindex",-1),"function"==typeof this.options.display&&(this.options.autotext="always"),this.options.autotext){case"always":e=!0;break;case"auto":e=!t.trim(this.$element.text()).length&&null!==this.value&&void 0!==this.value&&!i;break;default:e=!1}t.when(!e||this.render()).then(t.proxy(function(){this.options.disabled?this.disable():this.enable(),this.$element.triggerHandler("init",this)},this))}},initLive:function(){var e=this.options.selector;this.options.selector=!1,this.options.autotext="never",this.$element.on(this.options.toggle+".editable",e,t.proxy(function(i){var n=t(i.target).closest(e);n.data("editable")||(n.hasClass(this.options.emptyclass)&&n.empty(),n.editable(this.options).trigger(i))},this))},render:function(t){if(!1!==this.options.display)return this.input.value2htmlFinal?this.input.value2html(this.value,this.$element[0],this.options.display,t):"function"==typeof this.options.display?this.options.display.call(this.$element[0],this.value,t):this.input.value2html(this.value,this.$element[0])},enable:function(){this.options.disabled=!1,this.$element.removeClass("editable-disabled"),this.handleEmpty(this.isEmpty),"manual"!==this.options.toggle&&"-1"===this.$element.attr("tabindex")&&this.$element.removeAttr("tabindex")},disable:function(){this.options.disabled=!0,this.hide(),this.$element.addClass("editable-disabled"),this.handleEmpty(this.isEmpty),this.$element.attr("tabindex",-1)},toggleDisabled:function(){this.options.disabled?this.enable():this.disable()},option:function(e,i){if(e&&"object"==typeof e)t.each(e,t.proxy(function(e,i){this.option(t.trim(e),i)},this));else{if(this.options[e]=i,"disabled"===e)return i?this.disable():this.enable();"value"===e&&this.setValue(i),this.container&&this.container.option(e,i),this.input.option&&this.input.option(e,i)}},handleEmpty:function(e){!1!==this.options.display&&(void 0!==e?this.isEmpty=e:"function"==typeof this.input.isEmpty?this.isEmpty=this.input.isEmpty(this.$element):this.isEmpty=""===t.trim(this.$element.html()),this.options.disabled?this.isEmpty&&(this.$element.empty(),this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass)):this.isEmpty?(this.$element.html(this.options.emptytext),this.options.emptyclass&&this.$element.addClass(this.options.emptyclass)):this.options.emptyclass&&this.$element.removeClass(this.options.emptyclass))},show:function(e){if(!this.options.disabled){if(this.container){if(this.container.tip().is(":visible"))return}else{var i=t.extend({},this.options,{value:this.value,input:this.input});this.$element.editableContainer(i),this.$element.on("save.internal",t.proxy(this.save,this)),this.container=this.$element.data("editableContainer")}this.container.show(e)}},hide:function(){this.container&&this.container.hide()},toggle:function(t){this.container&&this.container.tip().is(":visible")?this.hide():this.show(t)},save:function(t,e){if(this.options.unsavedclass){var i=!1;(i=(i=(i=(i=i||"function"==typeof this.options.url)||!1===this.options.display)||void 0!==e.response)||this.options.savenochange&&this.input.value2str(this.value)!==this.input.value2str(e.newValue))?this.$element.removeClass(this.options.unsavedclass):this.$element.addClass(this.options.unsavedclass)}if(this.options.highlight){var n=this.$element,s=n.css("background-color");n.css("background-color",this.options.highlight),setTimeout(function(){"transparent"===s&&(s=""),n.css("background-color",s),n.addClass("editable-bg-transition"),setTimeout(function(){n.removeClass("editable-bg-transition")},1700)},10)}this.setValue(e.newValue,!1,e.response)},validate:function(){if("function"==typeof this.options.validate)return this.options.validate.call(this,this.value)},setValue:function(e,i,n){this.value=i?this.input.str2value(e):e,this.container&&this.container.option("value",this.value),t.when(this.render(n)).then(t.proxy(function(){this.handleEmpty()},this))},activate:function(){this.container&&this.container.activate()},destroy:function(){this.disable(),this.container&&this.container.destroy(),this.input.destroy(),"manual"!==this.options.toggle&&(this.$element.removeClass("editable-click"),this.$element.off(this.options.toggle+".editable")),this.$element.off("save.internal"),this.$element.removeClass("editable editable-open editable-disabled"),this.$element.removeData("editable")}},t.fn.editable=function(i){var n={},s=arguments;switch(i){case"validate":return this.each(function(){var e,i=t(this).data("editable");i&&(e=i.validate())&&(n[i.options.name]=e)}),n;case"getValue":return 2===arguments.length&&!0===arguments[1]?n=this.eq(0).data("editable").value:this.each(function(){var e=t(this).data("editable");e&&void 0!==e.value&&null!==e.value&&(n[e.options.name]=e.input.value2submit(e.value))}),n;case"submit":var o=arguments[1]||{},a=this,l=this.editable("validate");if(t.isEmptyObject(l)){var r={};if(1===a.length){var u=a.data("editable"),h={name:u.options.name||"",value:u.input.value2submit(u.value),pk:"function"==typeof u.options.pk?u.options.pk.call(u.options.scope):u.options.pk};"function"==typeof u.options.params?h=u.options.params.call(u.options.scope,h):(u.options.params=t.fn.editableutils.tryParseJson(u.options.params,!0),t.extend(h,u.options.params)),r={url:u.options.url,data:h,type:"POST"},o.success=o.success||u.options.success,o.error=o.error||u.options.error}else{var p=this.editable("getValue");r={url:o.url,data:p,type:"POST"}}r.success="function"==typeof o.success?function(t){o.success.call(a,t,o)}:t.noop,r.error="function"==typeof o.error?function(){o.error.apply(a,arguments)}:t.noop,o.ajaxOptions&&t.extend(r,o.ajaxOptions),o.data&&t.extend(r.data,o.data),t.ajax(r)}else"function"==typeof o.error&&o.error.call(a,l);return this}return this.each(function(){var n=t(this),o=n.data("editable"),a="object"==typeof i&&i;a&&a.selector?o=new e(this,a):(o||n.data("editable",o=new e(this,a)),"string"==typeof i&&o[i].apply(o,Array.prototype.slice.call(s,1)))})},t.fn.editable.defaults={type:"text",disabled:!1,toggle:"click",emptytext:"Empty",autotext:"auto",value:null,display:null,emptyclass:"editable-empty",unsavedclass:"editable-unsaved",selector:null,highlight:"#FFFF80"}}(window.jQuery),function(t){"use strict";t.fn.editabletypes={};var e=function(){};e.prototype={init:function(e,i,n){this.type=e,this.options=t.extend({},n,i)},prerender:function(){this.$tpl=t(this.options.tpl),this.$input=this.$tpl,this.$clear=null,this.error=null},render:function(){},value2html:function(e,i){t(i)[this.options.escape?"text":"html"](t.trim(e))},html2value:function(e){return t("<div>").html(e).text()},value2str:function(t){return String(t)},str2value:function(t){return t},value2submit:function(t){return t},value2input:function(t){this.$input.val(t)},input2value:function(){return this.$input.val()},activate:function(){this.$input.is(":visible")&&this.$input.focus()},clear:function(){this.$input.val(null)},escape:function(e){return t("<div>").text(e).html()},autosubmit:function(){},destroy:function(){},setClass:function(){this.options.inputclass&&this.$input.addClass(this.options.inputclass)},setAttr:function(t){void 0!==this.options[t]&&null!==this.options[t]&&this.$input.attr(t,this.options[t])},option:function(t,e){this.options[t]=e}},e.defaults={tpl:"",inputclass:null,escape:!0,scope:null,showbuttons:!0},t.extend(t.fn.editabletypes,{abstractinput:e})}(window.jQuery),function(t){"use strict";var e=function(t){};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){var e=t.Deferred();return this.error=null,this.onSourceReady(function(){this.renderList(),e.resolve()},function(){this.error=this.options.sourceError,e.resolve()}),e.promise()},html2value:function(t){return null},value2html:function(e,i,n,s){var o=t.Deferred(),a=function(){"function"==typeof n?n.call(i,e,this.sourceData,s):this.value2htmlFinal(e,i),o.resolve()};return null===e?a.call(this):this.onSourceReady(a,function(){o.resolve()}),o.promise()},onSourceReady:function(e,i){var n;if(t.isFunction(this.options.source)?(n=this.options.source.call(this.options.scope),this.sourceData=null):n=this.options.source,this.options.sourceCache&&t.isArray(this.sourceData))e.call(this);else{try{n=t.fn.editableutils.tryParseJson(n,!1)}catch(t){return void i.call(this)}if("string"==typeof n){if(this.options.sourceCache){var s,o=n;if(t(document).data(o)||t(document).data(o,{}),!1===(s=t(document).data(o)).loading&&s.sourceData)return this.sourceData=s.sourceData,this.doPrepend(),void e.call(this);if(!0===s.loading)return s.callbacks.push(t.proxy(function(){this.sourceData=s.sourceData,this.doPrepend(),e.call(this)},this)),void s.err_callbacks.push(t.proxy(i,this));s.loading=!0,s.callbacks=[],s.err_callbacks=[]}var a=t.extend({url:n,type:"get",cache:!1,dataType:"json",success:t.proxy(function(n){s&&(s.loading=!1),this.sourceData=this.makeArray(n),t.isArray(this.sourceData)?(s&&(s.sourceData=this.sourceData,t.each(s.callbacks,function(){this.call()})),this.doPrepend(),e.call(this)):(i.call(this),s&&t.each(s.err_callbacks,function(){this.call()}))},this),error:t.proxy(function(){i.call(this),s&&(s.loading=!1,t.each(s.err_callbacks,function(){this.call()}))},this)},this.options.sourceOptions);t.ajax(a)}else this.sourceData=this.makeArray(n),t.isArray(this.sourceData)?(this.doPrepend(),e.call(this)):i.call(this)}},doPrepend:function(){null!==this.options.prepend&&void 0!==this.options.prepend&&(t.isArray(this.prependData)||(t.isFunction(this.options.prepend)&&(this.options.prepend=this.options.prepend.call(this.options.scope)),this.options.prepend=t.fn.editableutils.tryParseJson(this.options.prepend,!0),"string"==typeof this.options.prepend&&(this.options.prepend={"":this.options.prepend}),this.prependData=this.makeArray(this.options.prepend)),t.isArray(this.prependData)&&t.isArray(this.sourceData)&&(this.sourceData=this.prependData.concat(this.sourceData)))},renderList:function(){},value2htmlFinal:function(t,e){},makeArray:function(e){var i,n,s,o,a=this.options.valueFieldName,l=this.options.textFieldName,r=[];if(!e||"string"==typeof e)return null;if(t.isArray(e)){o=function(t,e){if((n={})[a]=t,n[l]=e,i++>=2)return!1};for(var u=0;u<e.length;u++)if("object"==typeof(s=e[u]))i=0,t.each(s,o),1===i?r.push(n):i>1&&(s.children&&(s.children=this.makeArray(s.children)),r.push(s));else{var h={};h[a]=s,h[l]=s,r.push(h)}}else t.each(e,function(t,e){var i={};i[a]=t,i[l]=e,r.push(i)});return r},option:function(t,e){this.options[t]=e,"source"===t&&(this.sourceData=null),"prepend"===t&&(this.prependData=null)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{valueFieldName:"value",textFieldName:"text",source:null,prepend:!1,sourceError:"Error when loading list",sourceCache:!0,sourceOptions:null}),t.fn.editabletypes.list=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("text",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.renderClear(),this.setClass(),this.setAttr("placeholder")},activate:function(){this.$input.is(":visible")&&(this.$input.focus(),this.$input.is("input,textarea")&&!this.$input.is('[type="checkbox"],[type="range"]')&&t.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length),this.toggleClear&&this.toggleClear())},renderClear:function(){this.options.clear&&(this.$clear=t('<span class="editable-clear-x"></span>'),this.$input.after(this.$clear).css("padding-right",24).keyup(t.proxy(function(e){if(!~t.inArray(e.keyCode,[40,38,9,13,27])){clearTimeout(this.t);var i=this;this.t=setTimeout(function(){i.toggleClear(e)},100)}},this)).parent().css("position","relative"),this.$clear.click(t.proxy(this.clear,this)))},postrender:function(){},toggleClear:function(t){if(this.$clear){var e=this.$input.val().length,i=this.$clear.is(":visible");e&&!i&&this.$clear.show(),!e&&i&&this.$clear.hide()}},clear:function(){this.$clear.hide(),this.$input.val("").focus()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',placeholder:null,clear:!0}),t.fn.editabletypes.text=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("textarea",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass(),this.setAttr("placeholder"),this.setAttr("rows"),this.$input.keydown(function(e){e.ctrlKey&&13===e.which&&t(this).closest("form").submit()})},activate:function(){t.fn.editabletypes.text.prototype.activate.call(this)}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:"<textarea></textarea>",inputclass:"input-large",placeholder:null,rows:7}),t.fn.editabletypes.textarea=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("select",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){this.$input.empty();var e=this.options.escape,i=this.options.textFieldName,n=this.options.valueFieldName,s=function(o,a){var l;if(t.isArray(a))for(var r=0;r<a.length;r++)if(l={},a[r].children)l.label=a[r][i],o.append(s(t("<optgroup>",l),a[r].children));else{l.value=a[r][n],a[r].disabled&&(l.disabled=!0);var u=t("<option>",l);u[e?"text":"html"](a[r][i]),o.append(u)}return o};s(this.$input,this.sourceData),this.setClass(),this.$input.on("keydown.editable",function(e){13===e.which&&t(this).closest("form").submit()})},value2htmlFinal:function(e,i){var n="",s=t.fn.editableutils.itemsByValue(e,this.sourceData),o=this.options.textFieldName;s.length&&(n=s[0][o]),t.fn.editabletypes.abstractinput.prototype.value2html.call(this,n,i)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:"<select></select>"}),t.fn.editabletypes.select=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("selectize",t,e.defaults),this.sourceData=null};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){this.$input.empty();var e=this.options.escape,i=this.options.textFieldName,n=this.options.valueFieldName,s=function(o,a){var l;if(t.isArray(a))for(var r=0;r<a.length;r++)if(l={},a[r].children)l.label=a[r][i],o.append(s(t("<optgroup>",l),a[r].children));else{l.value=a[r][n],a[r].disabled&&(l.disabled=!0);var u=t("<option>",l);u[e?"text":"html"](a[r][i]),o.append(u)}return o};s(this.$input,this.sourceData),this.setClass(),this.$input.on("keydown.editable",function(e){13===e.which&&t(this).closest("form").submit()})},value2input:function(e){var i=this.options.sourceUrl,n=1,s=!1;this.options.multiple&&(s=!0,n=this.options.maxItems),i?this.$input.selectize({valueField:"value",labelField:"label",searchField:["label"],options:[],create:!0,maxItems:n,multiple:s,render:{option:function(t,e){return"<div>"+e(t.label)+"</div>"}},load:function(e,n){if(!e.length)return n();t.ajax({url:i+"/"+e,type:"GET",dataType:"json",error:function(){n()},success:function(t){n(t)}})}}):this.$input.selectize(this.options.selectize)},value2htmlFinal:function(e,i){var n="",s=t.fn.editableutils.itemsByValue(e,this.sourceData),o=this.options.textFieldName;n=s.length?s[0][o]:e,t.fn.editabletypes.abstractinput.prototype.value2html.call(this,n,i)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{selectize:{},maxItems:1,multiple:!1,sourceUrl:null,tpl:"<select></select>"}),t.fn.editabletypes.selectize=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("checklist",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){var e;if(this.$tpl.empty(),t.isArray(this.sourceData)){for(var i=0;i<this.sourceData.length;i++){e=t('<label class="custom-control custom-checkbox">').append(t("<input>",{type:"checkbox",value:this.sourceData[i][this.options.valueFieldName]}));var n=t('<span class="custom-control-label">');n[this.options.escape?"text":"html"](" "+this.sourceData[i][this.options.textFieldName]),e.append(n),t("<div>").append(e).appendTo(this.$tpl)}this.$input=this.$tpl.find('input[type="checkbox"]'),this.setClass()}},value2str:function(e){return t.isArray(e)?e.sort().join(t.trim(this.options.separator)):""},str2value:function(e){var i,n=null;return"string"==typeof e&&e.length?(i=new RegExp("\\s*"+t.trim(this.options.separator)+"\\s*"),n=e.split(i)):n=t.isArray(e)?e:[e],n},value2input:function(e){this.$input.prop("checked",!1),t.isArray(e)&&e.length&&this.$input.each(function(i,n){var s=t(n);t.each(e,function(t,e){s.val()==e&&s.prop("checked",!0)})})},input2value:function(){var e=[];return this.$input.filter(":checked").each(function(i,n){e.push(t(n).val())}),e},value2htmlFinal:function(e,i){var n=[],s=t.fn.editableutils.itemsByValue(e,this.sourceData),o=this.options.escape,a=this.options.textFieldName;s.length?(t.each(s,function(e,i){var s=o?t.fn.editableutils.escape(i[a]):i[a];n.push(s)}),t(i).html(n.join("<br>"))):t(i).empty()},activate:function(){this.$input.first().focus()},autosubmit:function(){this.$input.on("keydown",function(e){13===e.which&&t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<div class="editable-checklist"></div>',inputclass:"custom-control-input",separator:","}),t.fn.editabletypes.checklist=e}(window.jQuery),function(t){var e=function(t){this.init("radiolist",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.list),t.extend(e.prototype,{renderList:function(){var e;if(this.$tpl.empty(),t.isArray(this.sourceData)){for(var i=this.options.textFieldName,n=this.options.valueFieldName,s=0;s<this.sourceData.length;s++){var o=this.options.name||"default_name";(e=t('<label class="custom-control custom-radio">').append(t("<input>",{type:"radio",name:o,value:this.sourceData[s][n]}))).append(t('<span class="custom-control-label">').text(this.sourceData[s][i])),this.$tpl.append(e)}this.$input=this.$tpl.find('input[type="radio"]'),this.setClass()}},value2str:function(t){return t},str2value:function(t){return t},value2input:function(e){this.$input.each(function(i,n){t(n).val()==e&&t(n).prop("checked",!0)})},input2value:function(){return this.$input.filter(":checked").val()},value2htmlFinal:function(e,i){var n=this.options.textFieldName,s=this.options.valueFieldName;if(t.fn.editableutils.itemsByValue(e,this.sourceData).length){var o=this.sourceData.filter(function(t){if(t[s]==e)return t[n]})[0][n];t(i).html(t.fn.editableutils.escape(o))}else t(i).empty()},value2submit:function(t){return t},activate:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.list.defaults,{tpl:'<label class="editable-radiolist input-sm"></label>',inputclass:"custom-control-input",separator:",",name:"defaultname"}),t.fn.editabletypes.radiolist=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("password",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{value2html:function(e,i){e?t(i).text("[hidden]"):t(i).empty()},html2value:function(t){return null}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="password">'}),t.fn.editabletypes.password=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("email",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="email">'}),t.fn.editabletypes.email=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("url",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="url">'}),t.fn.editabletypes.url=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("tel",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="tel">'}),t.fn.editabletypes.tel=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("number",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.text),t.extend(e.prototype,{render:function(){e.superclass.render.call(this),this.setAttr("min"),this.setAttr("max"),this.setAttr("step")},postrender:function(){this.$clear&&this.$clear.css({right:24})}}),e.defaults=t.extend({},t.fn.editabletypes.text.defaults,{tpl:'<input type="number">',inputclass:"input-mini",min:null,max:null,step:null}),t.fn.editabletypes.number=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("range",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.number),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.filter("input"),this.setClass(),this.setAttr("min"),this.setAttr("max"),this.setAttr("step"),this.$input.on("input",function(){t(this).siblings("output").text(t(this).val())})},activate:function(){this.$input.focus()}}),e.defaults=t.extend({},t.fn.editabletypes.number.defaults,{tpl:'<input type="range"><output class="badge badge-primry" style="display: inline-block"></output>',inputclass:"input-medium"}),t.fn.editabletypes.range=e}(window.jQuery),function(t){"use strict";var e=function(t){this.init("time",t,e.defaults)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.setClass()}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="time">'}),t.fn.editabletypes.time=e}(window.jQuery),function(t){"use strict";var e=function(i){this.init("flatdatetimepicker",i,e.defaults),i.flatpickr=t.fn.editableutils.tryParseJson(i.flatpickr,!0),this.options.flatpickr=t.extend({},e.defaults.flatpickr,i.flatpickr)};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.$input=this.$tpl.find("input");var t=this.options.flatpickr;this.datePicker=this.$input.flatpickr(t)},value2html:function(e,i){if(e){var n=this.datePicker.config.altFormat;if(n){var s=new Date(e),o=this.datePicker.formatDate(s,n);t(i).html(o)}else t(i).html(e)}else t(i).empty()},html2value:function(t){return t},value2str:function(t){return t},str2value:function(t){return t},value2input:function(t){t&&this.datePicker.setDate(t)},input2value:function(){return this.$input.val()},activate:function(){this.$input.focus()},autosubmit:function(){this.$input.keydown(function(e){13===e.which&&t(this).closest("form").submit()})}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-datetimepicker"><input type="text"></div>',inputclass:null,flatpickr:{altInput:!0,allowInput:!0,inline:!0,enableTime:!0,dateFormat:"Y-m-d H:i:s",altFormat:"F j, Y H:i:s",mode:"single"}}),t.fn.editabletypes.flatdatetimepicker=e}(window.jQuery),function(t){var e=function(e,i){this.$element=t(e),this.$element.is("input")?(this.options=t.extend({},t.fn.combodate.defaults,i,this.$element.data()),this.init()):t.error("Combodate should be applied to INPUT element")};e.prototype={constructor:e,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]},this.$widget=t('<span class="combodate"></span>').html(this.getTemplate()),this.initCombos(),this.datetime=null,this.$widget.on("change","select",t.proxy(function(e){this.$element.val(this.getValue()).change(),this.options.smartDays&&(t(e.target).is(".month")||t(e.target).is(".year"))&&this.fillCombo("day")},this)),this.$widget.find("select").css("width","auto"),this.$element.hide().after(this.$widget),this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var e=this.options.template,i=this.$element.prop("disabled"),n=this.options.customClass;return t.each(this.map,function(t,i){i=i[0];var n=new RegExp(i+"+"),s=i.length>1?i.substring(1,2):i;e=e.replace(n,"{"+s+"}")}),e=e.replace(/ /g,"&nbsp;"),t.each(this.map,function(t,s){var o=(s=s[0]).length>1?s.substring(1,2):s;e=e.replace("{"+o+"}",'<select class="'+t+" "+n+'"'+(i?' disabled="disabled"':"")+"></select>")}),e},initCombos:function(){for(var t in this.map){var e=this.$widget.find("."+t);this["$"+t]=e.length?e:null,this.fillCombo(t)}},fillCombo:function(t){var e=this["$"+t];if(e){var i=this["fill"+t.charAt(0).toUpperCase()+t.slice(1)](),n=e.val();e.empty();for(var s=0;s<i.length;s++)e.append('<option value="'+i[s][0]+'">'+i[s][1]+"</option>");e.val(n)}},fillCommon:function(t){var e,i=[];if("name"===this.options.firstItem){var n="function"==typeof(e=moment.localeData?moment.localeData()._relativeTime:moment.relativeTime||moment.langData()._relativeTime)[t]?e[t](1,!0,t,!1):e[t];n=n.split(" ").reverse()[0],i.push(["",n])}else"empty"===this.options.firstItem&&i.push(["",""]);return i},fillDay:function(){var t,e,i=this.fillCommon("d"),n=-1!==this.options.template.indexOf("DD"),s=31;if(this.options.smartDays&&this.$month&&this.$year){var o=parseInt(this.$month.val(),10),a=parseInt(this.$year.val(),10);isNaN(o)||isNaN(a)||(s=moment([a,o]).daysInMonth())}for(e=1;e<=s;e++)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillMonth:function(){var t,e,i=this.fillCommon("M"),n=-1!==this.options.template.indexOf("MMMMMM"),s=-1!==this.options.template.indexOf("MMMMM"),o=-1!==this.options.template.indexOf("MMMM"),a=-1!==this.options.template.indexOf("MMM"),l=-1!==this.options.template.indexOf("MM");for(e=0;e<=11;e++)t=n?moment().date(1).month(e).format("MM - MMMM"):s?moment().date(1).month(e).format("MM - MMM"):o?moment().date(1).month(e).format("MMMM"):a?moment().date(1).month(e).format("MMM"):l?this.leadZero(e+1):e+1,i.push([e,t]);return i},fillYear:function(){var t,e,i=[],n=-1!==this.options.template.indexOf("YYYY");for(e=this.options.maxYear;e>=this.options.minYear;e--)t=n?e:(e+"").substring(2),i[this.options.yearDescending?"push":"unshift"]([e,t]);return i=this.fillCommon("y").concat(i)},fillHour:function(){var t,e,i=this.fillCommon("h"),n=-1!==this.options.template.indexOf("h"),s=(this.options.template.indexOf("H"),-1!==this.options.template.toLowerCase().indexOf("hh")),o=n?12:23;for(e=n?1:0;e<=o;e++)t=s?this.leadZero(e):e,i.push([e,t]);return i},fillMinute:function(){var t,e,i=this.fillCommon("m"),n=-1!==this.options.template.indexOf("mm");for(e=0;e<=59;e+=this.options.minuteStep)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillSecond:function(){var t,e,i=this.fillCommon("s"),n=-1!==this.options.template.indexOf("ss");for(e=0;e<=59;e+=this.options.secondStep)t=n?this.leadZero(e):e,i.push([e,t]);return i},fillAmpm:function(){var t=-1!==this.options.template.indexOf("a");this.options.template.indexOf("A");return[["am",t?"am":"AM"],["pm",t?"pm":"PM"]]},getValue:function(e){var i,n={},s=this,o=!1;return t.each(this.map,function(t,e){if("ampm"!==t){var i;if(s["$"+t])n[t]=parseInt(s["$"+t].val(),10);else i=s.datetime?s.datetime[e[1]]():"day"===t?1:0,n[t]=i;return isNaN(n[t])?(o=!0,!1):void 0}}),o?"":(this.$ampm&&(12===n.hour?n.hour="am"===this.$ampm.val()?0:12:n.hour="am"===this.$ampm.val()?n.hour:n.hour+12),i=moment([n.year,n.month,n.day,n.hour,n.minute,n.second]),this.highlight(i),null===(e=void 0===e?this.options.format:e)?i.isValid()?i:null:i.isValid()?i.format(e):"")},setValue:function(e){if(e){var i="string"==typeof e?moment(e,this.options.format,!0):moment(e),n=this,s={};i.isValid()?(t.each(this.map,function(t,e){"ampm"!==t&&(s[t]=i[e[1]]())}),this.$ampm&&(s.hour>=12?(s.ampm="pm",s.hour>12&&(s.hour-=12)):(s.ampm="am",0===s.hour&&(s.hour=12))),t.each(s,function(t,e){n["$"+t]&&("minute"===t&&n.options.minuteStep>1&&n.options.roundTime&&(e=o(n["$"+t],e)),"second"===t&&n.options.secondStep>1&&n.options.roundTime&&(e=o(n["$"+t],e)),n["$"+t].val(e))}),this.options.smartDays&&this.fillCombo("day"),this.$element.val(i.format(this.options.format)).change(),this.datetime=i):this.datetime=null}function o(e,i){var n={};return e.children("option").each(function(e,s){var o,a=t(s).attr("value");""!==a&&(o=Math.abs(a-i),(void 0===n.distance||o<n.distance)&&(n={value:a,distance:o}))}),n.value}},highlight:function(t){t.isValid()?this.options.errorClass?this.$widget.removeClass(this.options.errorClass):this.$widget.find("select").css("border-color",this.borderColor):this.options.errorClass?this.$widget.addClass(this.options.errorClass):(this.borderColor||(this.borderColor=this.$widget.find("select").css("border-color")),this.$widget.find("select").css("border-color","red"))},leadZero:function(t){return t<=9?"0"+t:t},destroy:function(){this.$widget.remove(),this.$element.removeData("combodate").show()}},t.fn.combodate=function(i){var n,s=Array.apply(null,arguments);return s.shift(),"getValue"===i&&this.length&&(n=this.eq(0).data("combodate"))?n.getValue.apply(n,s):this.each(function(){var n=t(this),o=n.data("combodate"),a="object"==typeof i&&i;o||n.data("combodate",o=new e(this,a)),"string"==typeof i&&"function"==typeof o[i]&&o[i].apply(o,s)})},t.fn.combodate.defaults={format:"YYYY-MM-DD HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:(new Date).getFullYear(),yearDescending:!0,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:null,customClass:"",roundTime:!0,smartDays:!1}}(window.jQuery),function(t){"use strict";var e=function(i){this.init("combodate",i,e.defaults),this.options.viewformat||(this.options.viewformat=this.options.format),i.combodate=t.fn.editableutils.tryParseJson(i.combodate,!0),this.options.combodate=t.extend({},e.defaults.combodate,i.combodate,{format:this.options.format,template:this.options.template})};t.fn.editableutils.inherit(e,t.fn.editabletypes.abstractinput),t.extend(e.prototype,{render:function(){this.$input.combodate(this.options.combodate),"bs3"===t.fn.editableform.engine&&this.$input.siblings().find("select").addClass("form-control"),this.options.inputclass&&this.$input.siblings().find("select").addClass(this.options.inputclass)},value2html:function(t,i){var n=t?t.format(this.options.viewformat):"";e.superclass.value2html.call(this,n,i)},html2value:function(t){return t?moment(t,this.options.viewformat):null},value2str:function(t){return t?t.format(this.options.format):""},str2value:function(t){return t?moment(t,this.options.format):null},value2submit:function(t){return this.value2str(t)},value2input:function(t){this.$input.combodate("setValue",t)},input2value:function(){return this.$input.combodate("getValue",null)},activate:function(){this.$input.siblings(".combodate").find("select").eq(0).focus()},autosubmit:function(){}}),e.defaults=t.extend({},t.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',inputclass:null,format:"YYYY-MM-DD",viewformat:null,template:"D / MMM / YYYY",combodate:null}),t.fn.editabletypes.combodate=e}(window.jQuery),function(t){"use strict";var e=t.fn.editableform.Constructor.prototype.initInput;t.extend(t.fn.editableform.Constructor.prototype,{initTemplate:function(){this.$form=t(t.fn.editableform.template),this.$form.find(".control-group").addClass("form-group"),this.$form.find(".editable-error-block").addClass("help-block")},initInput:function(){e.apply(this);var i=null===this.input.options.inputclass||!1===this.input.options.inputclass,n="text,select,textarea,password,email,url,tel,number,range,time,typeaheadjs".split(",");~t.inArray(this.input.type,n)&&(this.input.$input.addClass("form-control"),i&&(this.input.options.inputclass="form-control-sm",this.input.$input.addClass("form-control-sm")));for(var s=this.$form.find(".editable-buttons"),o=i?["form-control-sm"]:this.input.options.inputclass.split(" "),a=0;a<o.length;a++)"input-lg"===o[a].toLowerCase()&&s.find("button").removeClass("btn-sm").addClass("btn-lg")}}),t.fn.editableform.buttons='<button type="submit" class="btn btn-primary btn-sm editable-submit"><i class="fa fa-check" aria-hidden="true"></i></button><button type="button" class="btn btn-default btn-sm editable-cancel"><i class="fa fa-times" aria-hidden="true"></i></button>',t.fn.editableform.errorGroupClass="has-error",t.fn.editableform.errorBlockClass=null,t.fn.editableform.engine="bs4"}(window.jQuery),function(t){"use strict";t.extend(t.fn.editableContainer.Popup.prototype,{containerName:"popover",containerDataName:"bs.popover",innerCss:".popover-body",defaults:t.fn.popover.Constructor.DEFAULTS,initContainer:function(){var e;t.extend(this.containerOptions,{trigger:"manual",selector:!1,content:" ",template:this.defaults.template}),this.$element.data("template")&&(e=this.$element.data("template"),this.$element.removeData("template")),this.call(this.containerOptions),e&&this.$element.data("template",e)},innerShow:function(){this.call("show")},innerHide:function(){this.call("hide")},innerDestroy:function(){this.call("dispose")},setContainerOption:function(t,e){this.container().options[t]=e},setPosition:function(){(function(){}).call(this.container())},tip:function(){return this.container()?t(this.container().tip):null}})}(window.jQuery);