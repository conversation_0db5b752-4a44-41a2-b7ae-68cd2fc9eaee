-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- W<PERSON><PERSON> pembuatan: 19 Jul 2025 pada 03.41
-- Versi server: 10.4.27-MariaDB
-- Versi PHP: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `pemin<PERSON>man`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON>tur dari tabel `kelas`
--

CREATE TABLE `kelas` (
  `id_kelas` int(11) NOT NULL,
  `nama_kelas` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data untuk tabel `kelas`
--

INSERT INTO `kelas` (`id_kelas`, `nama_kelas`) VALUES
(1, 'KPP A'),
(2, 'KPP B'),
(3, 'KPP C'),
(4, 'X - 1'),
(5, 'X - 2'),
(6, 'XI - 1'),
(7, 'XI - 2'),
(8, 'XII - 1'),
(9, 'XII - 2'),
(10, 'KPA');

-- --------------------------------------------------------

--
-- Struktur dari tabel `peminjaman`
--

CREATE TABLE `peminjaman` (
  `id_peminjaman` int(11) NOT NULL,
  `tanggal_pinjam` date NOT NULL,
  `nama` varchar(255) NOT NULL,
  `kelas` varchar(255) NOT NULL,
  `barang` varchar(255) NOT NULL,
  `tanggal_kembali` date NOT NULL,
  `kegiatan` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `peminjaman`
--

INSERT INTO `peminjaman` (`id_peminjaman`, `tanggal_pinjam`, `nama`, `kelas`, `barang`, `tanggal_kembali`, `kegiatan`) VALUES
(7, '2024-09-17', '130', '9', 'Kunci Lab Bahasa', '2024-09-17', ''),
(8, '2024-09-17', '31', '5', 'Kunci Ruang Rapat Guru', '2024-09-18', ''),
(9, '2024-09-19', '106', '8', 'Kunci Lab Multimedia', '2024-09-19', ''),
(10, '2024-09-19', '81', '6', 'Kunci Lab Sosial', '2024-09-19', ''),
(11, '2024-09-20', '52', '5', 'Kunci Lab Multimedia', '0000-00-00', ''),
(12, '2024-09-28', '130', '9', 'Sound Woofer', '2024-09-30', ''),
(13, '2024-09-30', '19', '4', 'Kunci Ruang Rapat Guru', '2024-10-01', ''),
(14, '2024-10-02', '106', '8', 'Kunci Lab Multimedia', '2024-10-02', ''),
(15, '2024-10-03', '197', '10', 'Sound Kecil', '2024-10-03', ''),
(16, '2024-10-07', '89', '6', 'Kunci Lab Bahasa', '2024-10-07', ''),
(17, '2024-10-10', '111', '8', 'Kunci Lab Kimia', '2024-10-10', ''),
(18, '2024-10-10', '63', '6', 'Sound Kecil', '2024-10-10', ''),
(19, '2024-10-10', '108', '9', 'Kunci Lab Multimedia', '2024-10-11', ''),
(20, '2024-10-11', '102', '6', 'Kunci Lab Multimedia', '2024-10-11', ''),
(21, '2024-10-11', '111', '8', 'Kunci Lab Kimia', '2024-10-11', ''),
(22, '2024-10-11', '117', '8', 'Kunci Lab Fisika', '2024-10-11', ''),
(23, '2024-10-11', '117', '8', 'Kunci Lab Kimia', '2024-12-19', ''),
(24, '2024-10-15', '129', '9', 'Sound Kecil', '0000-00-00', ''),
(25, '2024-10-17', '72', '6', 'Kunci Lab Multimedia', '2024-10-17', ''),
(26, '2024-10-17', '8', '4', 'Kunci Lab Bahasa', '2024-10-17', ''),
(27, '2024-10-21', '99', '6', 'Kunci Lab Bahasa', '2024-10-21', ''),
(28, '2024-10-22', '197', '10', 'Kunci Lab Multimedia', '2024-10-22', ''),
(29, '2024-10-23', '106', '8', 'Kunci Lab Bahasa', '2024-10-23', ''),
(30, '2024-10-23', '143', '8', 'Kunci Lab Multimedia', '2024-10-23', ''),
(31, '2024-10-24', '196', '10', 'Kunci Lab Multimedia', '2024-10-24', ''),
(32, '2024-10-24', '50', '5', 'Sound Kecil', '2024-10-24', ''),
(33, '2024-10-29', '29', '5', 'Sound Woofer', '2024-10-29', ''),
(34, '2024-10-31', '78', '7', 'Sound Woofer', '2024-10-31', ''),
(35, '2024-10-31', '68', '7', 'Kabel HDMI', '2024-11-01', ''),
(36, '2024-11-02', '195', '10', 'Laptop', '2024-11-02', ''),
(37, '2024-11-04', '50', '5', 'Kunci Lab Bahasa', '2024-11-04', ''),
(39, '2024-11-06', '93', '6', 'Proyektor', '2024-11-06', ''),
(40, '2024-11-09', '119', '9', 'Kunci Lab Kompunter', '2024-11-09', ''),
(41, '2024-11-11', '125', '8', 'Kunci Lab Kimia', '2024-11-11', ''),
(42, '2024-11-14', '27', '4', 'Kunci Lab Fisika', '2024-11-16', ''),
(43, '2024-11-14', '27', '4', 'Kunci Lab Kimia', '2024-11-16', ''),
(44, '2024-11-14', '27', '4', 'Kunci Lab Biologi', '2024-11-16', ''),
(45, '2024-11-14', '10', '4', 'Proyektor', '2024-11-15', ''),
(46, '2024-12-16', '75', '6', 'Kunci Ruang Rapat Guru', '2024-12-16', ''),
(47, '2025-01-07', '190', '10', 'Sound Kecil', '2025-01-07', ''),
(48, '2025-01-07', '84', '6', 'Sound Kecil', '2025-01-07', ''),
(49, '2025-01-04', '209', '1', 'Sound Woofer', '2025-01-07', ''),
(50, '2025-01-15', '190', '10', 'Sound Kecil', '2025-01-15', ''),
(51, '2025-01-15', '198', '10', 'Sound Kecil', '2025-01-15', ''),
(52, '2025-01-16', '57', '7', 'Kunci Lab Multimedia', '2025-01-17', ''),
(53, '2025-01-21', '143', '8', 'Sound Kecil', '2025-01-22', ''),
(54, '2025-02-06', '102', '6', 'Kunci Lab Sosial', '0000-00-00', 'Sidang Karya Tulis'),
(55, '2025-02-11', '191', '10', 'Kunci Lab Multimedia', '2025-02-11', 'pelajaran menulis pak Toto'),
(56, '2025-02-11', '59', '6', 'Proyektor', '0000-00-00', 'ujian karya tulis'),
(57, '2025-02-14', '78', '7', 'Sound Kecil', '0000-00-00', 'mapel Fisika'),
(58, '2025-02-18', '126', '8', 'Sound Woofer', '2025-02-18', 'ujian praktek'),
(59, '2025-02-18', '152', '8', 'Sound Kecil', '0000-00-00', 'ujian praktek'),
(61, '2025-02-18', '199', '10', 'Kunci Ruang Rapat Guru', '0000-00-00', 'rapatbersama ipkasma'),
(62, '2025-02-19', '152', '8', 'Kunci Lab Kompunter', '2025-02-20', 'ujian praktek'),
(63, '2025-02-19', '143', '8', 'Kunci Lab Bahasa', '2025-02-20', 'simulasi webinar'),
(64, '2025-02-19', '152', '8', 'Kunci Ruang Rapat Guru', '2025-02-20', 'ujian praktek'),
(65, '2025-02-19', '152', '8', 'Kunci Lab Multimedia', '2025-02-20', 'ujian praktik webinar'),
(66, '2025-02-19', '152', '8', 'Kunci Lab Kompunter', '2025-02-20', 'ujian praktek'),
(67, '2025-02-20', '152', '8', 'Kunci Lab Bahasa', '2025-02-20', 'ujian praktek'),
(68, '2025-02-20', '143', '8', 'Kunci Ruang Rapat Guru', '2025-02-20', 'webinar'),
(69, '2025-02-28', '78', '7', 'Sound Kecil', '2025-02-28', 'Pelajaran fisika'),
(70, '2025-03-07', '256', '3', 'Kunci Lab Multimedia', '2025-03-07', 'untuk pembelajaran mrs ling ling'),
(71, '2025-03-10', '104', '6', 'Kunci Lab Kimia', '2025-03-10', 'Meminjam baju praktik untuk SA'),
(72, '2025-03-14', '98', '7', 'Laptop', '2025-03-14', 'pelajaran sosiologi'),
(73, '2025-03-22', '100', '7', 'Sound Woofer', '2025-03-22', 'sosialisasi pokmin'),
(74, '2025-03-25', '102', '6', 'Kunci Lab Multimedia', '2025-03-25', 'brg hlg'),
(75, '2025-03-25', '57', '7', 'Kunci Lab Multimedia', '2025-03-25', 'Spes Minat'),
(76, '2025-04-07', '50', '5', 'Proyektor', '2025-04-07', 'P5 Presentasi'),
(77, '2025-04-12', '118', '9', 'Kunci Ruang Rapat Guru', '2025-04-12', 'Tes Masuk universitas 13.00 jam 08.00 (kunci dititipkan ke kepamongan MU)'),
(78, '2025-04-22', '18', '5', 'Kunci Lab Multimedia', '0000-00-00', 'pelajaran bahasa inggris'),
(79, '2025-04-22', '94', '7', 'Kunci Lab Multimedia', '2025-04-22', 'bahasa jawa'),
(80, '2025-04-25', '61', '6', 'Laptop', '2025-04-25', 'sosiologi'),
(81, '2025-04-28', '50', '5', 'Proyektor', '2025-04-29', 'pleno tengah osis'),
(82, '2025-05-13', '63', '6', 'Laptop', '2025-05-13', 'P5 Presentasi'),
(83, '2025-06-10', '50', '5', 'Proyektor', '2025-06-10', 'sosialisasi kartul'),
(84, '2025-06-14', '50', '5', 'Proyektor', '2025-06-14', 'pleno akhir osis');

-- --------------------------------------------------------

--
-- Struktur dari tabel `pengambilan_atk`
--

CREATE TABLE `pengambilan_atk` (
  `id_pengambilan_atk` int(11) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `kelas` varchar(255) NOT NULL,
  `nama_barang` varchar(255) NOT NULL,
  `tanggal` date NOT NULL,
  `jumlah` varchar(255) NOT NULL,
  `nama_barang_2` varchar(255) NOT NULL,
  `jumlah_2` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `pengambilan_atk`
--

INSERT INTO `pengambilan_atk` (`id_pengambilan_atk`, `nama`, `kelas`, `nama_barang`, `tanggal`, `jumlah`, `nama_barang_2`, `jumlah_2`) VALUES
(8, '29', '5', 'Spidol', '2024-08-28', '1', '', ''),
(10, '8', '4', 'Spidol', '2024-08-28', '1', 'Reffil Spidol', '1'),
(11, '13', '5', 'Spidol', '2024-08-28', '1', '', ''),
(12, '258', '3', 'Spidol', '2024-09-03', '1', 'Reffil Spidol', '1'),
(13, '209', '1', 'Spidol', '2024-09-03', '2', '', ''),
(14, '151', '9', 'Reffil Tinta', '2024-09-03', '1', '', ''),
(15, '18', '5', 'Reffil Tinta', '2024-09-06', '2', '', ''),
(16, '201', '10', 'Reffil Tinta', '2024-09-09', '1', 'Reffil Spidol', '1'),
(17, '142', '9', 'Spidol', '2024-09-18', '1', 'Reffil Spidol', '1'),
(18, '205', '1', 'Reffil Tinta', '2024-09-18', '1', '', ''),
(19, '261', '3', 'Reffil Tinta', '2024-09-18', '2', '', ''),
(20, '232', '2', 'Spidol', '2024-09-23', '3', '', ''),
(21, '232', '2', 'Penghapus', '2024-09-23', '1', '', ''),
(22, '204', '1', 'Spidol', '2024-09-24', '2', '', ''),
(23, '193', '10', 'Reffil Tinta', '2024-09-26', '1', '', ''),
(24, '29', '5', 'Reffil Tinta', '2024-09-27', '1', 'Reffil Spidol', '1'),
(25, '236', '2', 'Spidol', '2024-09-27', '1', '', ''),
(26, '147', '9', 'Reffil Tinta', '2024-09-27', '1', '', ''),
(27, '152', '8', 'Spidol', '2024-09-28', '2', '', '1'),
(28, '202', '10', 'Spidol', '2024-09-28', '1', 'Reffil Spidol', '1'),
(29, '65', '7', 'Spidol', '2024-10-01', '1', 'Reffil Spidol', '1'),
(30, '220', '1', 'Spidol', '2024-10-02', '1', 'Reffil Spidol', '1'),
(31, '232', '2', 'Reffil Tinta', '2024-10-03', '1', '', ''),
(32, '152', '8', 'Spidol', '2024-10-04', '2', '', ''),
(33, '6', '4', 'Reffil Tinta', '2024-10-10', '1', '', '1'),
(34, '87', '6', 'Spidol', '2024-10-11', '1', 'Reffil Spidol', '1'),
(35, '114', '8', 'Spidol', '2024-10-12', '2', '', ''),
(36, '8', '4', 'Penghapus', '2024-10-14', '1', '', ''),
(37, '96', '7', 'Spidol', '2024-10-21', '2', '', ''),
(38, '40', '5', 'Reffil Tinta', '2024-10-22', '1', '', ''),
(39, '8', '4', 'Spidol', '2024-10-29', '1', '', ''),
(40, '114', '8', 'Reffil Tinta', '2024-10-29', '1', '', ''),
(41, '205', '1', 'Spidol', '2024-10-30', '2', '', ''),
(42, '65', '7', 'Penghapus', '2024-10-30', '1', '', ''),
(43, '131', '8', 'Spidol', '2024-10-31', '1', '', ''),
(44, '258', '3', 'Spidol', '2024-11-04', '2', '', ''),
(45, '8', '4', 'Reffil Tinta', '2024-11-04', '1', '', ''),
(46, '84', '6', 'Spidol', '2024-11-05', '2', '', ''),
(47, '29', '5', 'Reffil Tinta', '2024-11-05', '1', '', ''),
(48, '30', '4', 'Spidol', '2024-11-06', '2', '', ''),
(49, '2', '5', 'Reffil Tinta', '2024-11-06', '1', 'Spidol', '1'),
(50, '5', '4', 'Reffil Tinta', '2024-11-06', '1', '', ''),
(51, '205', '1', 'Spidol', '2024-11-07', '2', '', ''),
(52, '232', '2', 'Spidol', '2024-11-07', '2', 'Reffil Spidol', '1'),
(53, '205', '1', 'Reffil Tinta', '2024-11-13', '2', '', ''),
(54, '205', '1', 'Spidol', '2024-11-18', '1', '', ''),
(55, '87', '6', 'Spidol', '2024-11-25', '2', 'Reffil Spidol', '1'),
(56, '142', '9', 'Spidol', '2025-01-04', '2', '', ''),
(57, '122', '9', 'Spidol', '2025-01-04', '2', '', ''),
(58, '3', '4', 'Reffil Tinta', '2025-01-15', '2', '', ''),
(59, '114', '8', 'Reffil Tinta', '2025-01-16', '1', '', ''),
(60, '150', '9', 'Spidol', '2025-01-16', '2', '', ''),
(61, '63', '6', 'Reffil Tinta', '2025-01-17', '2', '', ''),
(62, '63', '6', 'Penghapus', '2025-01-18', '1', '', ''),
(64, '29', '5', 'Reffil Tinta', '2025-02-06', '1', 'Spidol', '1'),
(65, '224', '2', 'Spidol', '2025-02-07', '2', 'Reffil Spidol', '1'),
(66, '142', '9', 'Spidol', '2025-02-11', '1', 'Reffil Spidol', '1'),
(67, '63', '6', 'Spidol', '2025-02-12', '2', 'Reffil Spidol', '1'),
(68, '9', '5', 'Spidol', '2025-02-14', '2', 'Reffil Spidol', '1'),
(69, '86', '7', 'Reffil Tinta', '2025-02-17', '1', '', ''),
(70, '142', '9', 'Spidol', '2025-02-24', '2', '', ''),
(71, '61', '6', 'Spidol', '2025-02-25', '2', 'Reffil Spidol', '1'),
(72, '133', '8', 'Spidol', '2025-02-25', '1', 'Reffil Spidol', '1'),
(73, '147', '9', 'Spidol', '2025-02-27', '2', 'Reffil Spidol', '1'),
(74, '105', '7', 'Spidol', '2025-02-27', '3', '', ''),
(75, '236', '2', 'Spidol', '2025-02-26', '2', 'Reffil Spidol', '1'),
(76, '3', '4', 'Spidol', '2025-03-05', '2', 'Penghapus', '1'),
(77, '253', '3', 'Reffil Tinta', '2025-03-05', '2', 'Penghapus', '1'),
(78, '217', '1', 'Spidol', '2025-03-10', '1', 'Reffil Spidol', '1'),
(79, '9', '5', 'Reffil Tinta', '2025-03-15', '1', '', ''),
(80, '7', '4', 'Spidol', '2025-03-15', '2', 'Reffil Spidol', '2'),
(81, '94', '7', 'Reffil Tinta', '2025-03-24', '1', '', ''),
(82, '202', '10', 'Reffil Tinta', '2025-04-04', '1', 'Reffil Spidol', ''),
(83, '236', '2', 'Spidol', '2025-04-05', '2', '', ''),
(84, '63', '6', 'Spidol', '2025-04-28', '3', '', ''),
(85, '236', '2', 'Spidol', '2025-05-05', '2', '', ''),
(86, '236', '2', 'Spidol', '2025-05-05', '2', '', ''),
(87, '236', '2', 'Spidol', '2025-05-05', '2', '', ''),
(88, '1', '5', 'Reffil Tinta', '2025-05-05', '1', '', ''),
(89, '211', '1', 'Spidol', '2025-05-15', '1', 'Spidol', '1'),
(90, '94', '7', 'Spidol', '2025-05-16', '1', '', '1'),
(91, '63', '6', 'Spidol', '2025-05-21', '2', '', ''),
(92, '217', '1', 'Reffil Tinta', '2025-05-28', '1', 'Spidol', '2'),
(93, '224', '2', 'Spidol', '2025-06-09', '1', '', '');

-- --------------------------------------------------------

--
-- Struktur dari tabel `siswa`
--

CREATE TABLE `siswa` (
  `id_siswa` int(11) NOT NULL,
  `nis` varchar(255) NOT NULL,
  `nisn` varchar(255) NOT NULL,
  `nama_siswa` varchar(255) NOT NULL,
  `id_kelas` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data untuk tabel `siswa`
--

INSERT INTO `siswa` (`id_siswa`, `nis`, `nisn`, `nama_siswa`, `id_kelas`) VALUES
(1, '3727', '0089679814', 'Alexander Bancar Cakradara', '5'),
(2, '3733', '0086878885', 'Christopher Lebdo Kusumo', '5'),
(3, '3734', '0083124697', 'Cosmas Aldrick Judanto Hasudungan', '4'),
(4, '3737', '0082313678', 'Dionisius Pratista Aryasatya', '4'),
(5, '3739', '0081523216', 'Emanuel Ardi Chrisantana', '4'),
(6, '3758', '0078593139', 'Novandri Vito Mulyana', '4'),
(7, '3745', '0081263563', 'Gabriel Pramana Pradnyamurti', '4'),
(8, '3748', '0084843581', 'Joseph Nicolas Ope Lasaren', '4'),
(9, '3753', '0073780467', 'Leo Agung Daniel Cahya', '5'),
(10, '3756', '0082946085', 'Narendra Setya Nugraha', '4'),
(11, '3768', '0084442363', 'Raphael Sakti Kharisma Permana', '5'),
(12, '3772', '0081878293', 'Stephen Benedict Waluyantoro', '4'),
(13, '3773', '0079651729', 'Theophane Venard Vederico Dalihan', '5'),
(15, '3775', '0082113952', 'Yohanes Advendnando Christyan Adhy Tama', '4'),
(17, '3777', '0081051167', 'Yohanes Vincentius Krissanto', '4'),
(18, '3778', '0084813786', 'Yulianus Bintang Satria', '5'),
(19, '3728', '0087799913', 'Alexander Bintang Mahardika', '4'),
(20, '3730', '0086679491', 'Benediktus Francesco Jonas', '5'),
(21, '3731', '0076072038', 'Bernardus Bhanu Sandya Riyanto', '5'),
(22, '3732', '0074320112', 'Bonifasius Banyu Arga Lare Herawan', '4'),
(23, '3735', '0009711080', 'David Abraham Firdaus', '4'),
(24, '3736', '0088893643', 'Dean Theodorus Surbakti', '4'),
(25, '3738', '0088359035', 'Eduardo Daomara Samosir', '5'),
(26, '3740', '0077296476', 'Felix Novan Sitio', '5'),
(27, '3744', '0081799342', 'Gabriel Manuela Nararya Saragih', '4'),
(28, '3749', '0086906012', 'Karol Kenji', '5'),
(29, '3755', '0086568088', 'Michael Jonathan Prabowo', '5'),
(30, '3759', '0087182574', 'Parsaoran Pratama Yudha Simanjorang', '4'),
(31, '3761', '0073613219', 'Philipus Indra Pratam Paramasatya', '5'),
(32, '3766', '0084422784', 'Rafael Kasihraya Rahina', '4'),
(33, '3769', '0071849235', 'Robertus Airlangga Mahendra Atmaja', '5'),
(34, '3771', '0096435896', 'Samuel Geovan Alexandria Gabeler', '4'),
(35, '3774', '0082181328', 'William Sebastian Ressa', '4'),
(36, '3776', '0081744178', 'Yohanes Damai Kristian', '5'),
(38, '3729', '0081486953', 'Babtista Saktiawan Gagah Wicaksono', '4'),
(39, '3741', '0085846473', 'Frederick Swabuana Sayres Basundara Diandy', '4'),
(40, '3742', '0092399017', 'Fulgensius Russel Rahadian', '5'),
(41, '3743', '0085596605', 'Gabriel Dean Pandya Jaya', '5'),
(42, '3746', '0088444576', 'Gregorius Kharisma Ardy Firmani', '5'),
(43, '3747', '0081357938', 'Hilarius Amyas Balapradana', '5'),
(44, '3750', '0081072988', 'Kristofer Batubara', '4'),
(45, '3751', '0085224799', 'Kristoforus Benediktus Palimbong', '5'),
(46, '3752', '0087774774', 'Kristoforus Marcellino Mulya Putranto', '5'),
(47, '3754', '0088222079', 'Mario Bryan Tegar Kurniawan', '4'),
(48, '3757', '0084943230', 'Nikolaus Damar Kristanto', '5'),
(49, '3760', '0085422373', 'Peter Simon Galih Widyo Prasetyo', '4'),
(50, '3762', '0082871097', 'Pius Fredericus Rekyan Wijaya', '5'),
(51, '3763', '0076176737', 'Pius Koronkadeo Parikesit Wijaya', '4'),
(52, '3764', '0088299478', 'Raden Ageng Paskah Aji Suseto', '5'),
(53, '3765', '0071447106', 'Rafael Gerard Benito', '5'),
(54, '3767', '0079224412', 'Rafael Kewisa Kabelen', '5'),
(55, '3770', '0084319958', 'Rohfel Adyaraka Christianugrah Puspoasmoro', '4'),
(56, '3678', '0078364870', 'Albertus Raynard Taurean', '7'),
(57, '3680', '0076655963', 'Alessandro Brilliant Subandi Putra', '7'),
(58, '3682', '0072925509', 'Andreas Brahma Aditya Putra Arianto', '7'),
(59, '3684', '0075593139', 'Anselmus Rivaldo Gultom', '6'),
(60, '3686', '0062569719', 'Antonius Yoga Renaldi', '7'),
(61, '3688', '0073892433', 'Aurelio Dave Hermawan', '6'),
(62, '3690', '0074402900', 'Bernardus Bimo Wicaksono', '6'),
(63, '3692', '0076510467', 'Dionisius Ega Suparno', '6'),
(64, '3693', '0071099263', 'Edward Parahita Candrakumara Manumoyoso', '6'),
(65, '3694', '0077123622', 'Fillipus Bagus Winnandustiar', '7'),
(66, '3698', '0078453131', 'Fulgentius Raffayoga Grafinata', '7'),
(68, '3700', '0079677740', 'Ignatius Abby Kacaya Wisanta Nurpatria', '7'),
(69, '3702', '0065168784', 'Ignatius Satria Putra', '7'),
(70, '3704', '0076987543', 'Jalu Harya Garditya', '7'),
(71, '3706', '0071192118', 'Joseph Patricio Dwi Nugroho', '6'),
(72, '3708', '0062884195', 'Laurensius Ruiz Dharma Setiawan', '6'),
(73, '3709', '0079692826', 'Liberty Anggito Abimanyu', '6'),
(74, '3712', '0064131945', 'Michael Christovaldo Deandra Nathanael', '7'),
(75, '3714', '0078665569', 'Michael Moncer Rahina', '6'),
(77, '3718', '0074181931', 'Philipus Tottisa Tery Santosa', '7'),
(78, '3722', '0072976689', 'Vercellio Aviel Eleanor', '7'),
(79, '3724', '0078684981', 'Vincentius Paschalis Radhytia Christanto', '6'),
(80, '3726', '0068880313', 'Yohanes Dhonny Prihantoro', '6'),
(81, '3679', '0074793084', 'Aleksander Bagus Kusuma Adi D.', '6'),
(82, '3681', '0067130311', 'Aloysius Lovandhika Yulianto', '6'),
(83, '3683', '0076703194', 'Anselmus Prasetya Rimbawan', '7'),
(84, '3685', '0072588583', 'Antonius Gefana Wahyu Pratama', '6'),
(85, '3687', '0063985941', 'Anung Gantari', '6'),
(86, '3689', '0078770706', 'Benediktus Daniel Adhinata', '7'),
(87, '3691', '0077619948', 'Christopher Adrian Given Putra', '6'),
(88, '3695', '0069633771', 'Francisco Surya', '7'),
(89, '3696', '0077328300', 'Fransisco Diaz Galang Braja Nala', '6'),
(90, '3697', '0074985351', 'Fransiscus Xaverius Justin Lemuel Wijaya', '6'),
(91, '3701', '0074637409', 'Ignatius Aurel Tito Wijaya', '7'),
(92, '3703', '0067484911', 'Immanuel Mahatma Jawata', '6'),
(93, '3705', '0077575286', 'Jefferson Cliff Mappadang', '6'),
(94, '3707', '0077882254', 'Julius Belvo Aldian', '7'),
(96, '3710', '0079222703', 'Marselinus Melvin Ananda Jeanies', '7'),
(97, '3711', '0077207336', 'Michael Agung Prasetyawan Giusti', '6'),
(98, '3713', '0065513087', 'Michael Farrelino Cahyo Yudanto', '7'),
(99, '3715', '0079248782', 'Mikael Faustino Toda Kaha', '6'),
(100, '3717', '0065680194', 'Nathanael Satriya Genggam Darma', '7'),
(101, '3719', '0064992367', 'Raymundus Ardo Jatmiko', '7'),
(102, '3720', '0074560970', 'Reinard Sandya Wisanggeni', '6'),
(103, '3721', '0072903705', 'Reinardus Jagad Satria Wicaksana', '7'),
(104, '3723', '0069868950', 'Vercellio Ellyson Darren', '6'),
(105, '3725', '0078176875', 'William Wijaya Kurniawan', '7'),
(106, '3628', '0061973269', 'Agustino Dua Putra', '8'),
(107, '3631', '0062790255', 'Benedictus Steven Subiakto', '9'),
(108, '3632', '0067804114', 'Benediktus Yossiawan Teddy Nugroho', '9'),
(109, '3633', '0062950800', 'Bernardetus Risang Lanang Kristyani', '9'),
(111, '3637', '0078797700', 'Christoporus Sampurna Lidian', '8'),
(113, '3640', '0066064590', 'Filipus Agung Nugroho', '9'),
(114, '3642', '0061819544', 'Francesco Ryan Indra Prasetya', '8'),
(117, '3648', '0055691072', 'Gusti Kresna Wisnusiwi', '8'),
(118, '3649', '0068355902', 'Hieronymus Halashon Samosir', '9'),
(119, '3651', '0068048469', 'Ignatius Waradana Seto Ardhani', '9'),
(120, '3654', '0065547900', 'Joseph Billyarta', '8'),
(122, '3657', '0052577699', 'Joseph Quathino Putranto Wibowo', '9'),
(124, '3662', '0064459811', 'Mikael Dirgayusa', '9'),
(125, '3665', '0068101584', 'Nicodemus Ruben Banjamahor', '8'),
(126, '3666', '0066999495', 'Patrisius Widi Nugraha', '8'),
(129, '3676', '0059438056', 'Vinsensius Rio Nugroho', '9'),
(130, '3630', '0065841250', 'Benedictus Bima Satria Aji', '9'),
(131, '3634', '0068543427', 'Bernardin Mario Sandya Putra', '8'),
(132, '3638', '0056585160', 'Davin Firmansyah', '9'),
(133, '3641', '0069007055', 'Fillipus Krisna Nugraha Putra', '8'),
(134, '3644', '0061768555', 'FX. Ragil Ismupranatal Bhatara Randa', '8'),
(135, '3647', '0067229524', 'Gunawan Wiharjo', '8'),
(136, '3650', '0053651384', 'Hilarius Higa Septama', '8'),
(137, '3653', '0063302691', 'Joseph Alexander Gracius', '8'),
(138, '3661', '0057614898', 'Matheus Igosamon Cahyadi', '9'),
(139, '3663', '0065417913', 'Mikhael Noven Dewandaru', '9'),
(140, '3673', '0061018723', 'Thomas Aquinas Aditya', '8'),
(141, '3674', '0063595830', 'Titus Dwipa Danar Megantara', '9'),
(142, '3675', '0058881026', 'V. Maria Aditya Agiel Dy', '9'),
(143, '3677', '0053227904', 'Yosua Deo Basado', '8'),
(144, '3629', '0068328532', 'Andreas Rizky Abimanyu', '9'),
(145, '3636', '0066643570', 'Bonifasius Merkel Wijayandanu', '9'),
(147, '3652', '0069558265', 'J. Christiano Putra Alpa Regineus', '9'),
(148, '3655', '0063288285', 'Joseph Christopher Seto Setiawan', '8'),
(150, '3664', '0062029733', 'Nathanael Tanadrosa', '9'),
(151, '3667', '0018191021', 'Pilipus Surya Wardhana', '9'),
(152, '3670', '0058375010', 'Roney Karol Litoya Surbakti', '8'),
(153, '3672', '0062768860', 'Sebastianus Tito Kurniawan', '8'),
(190, 'A24.279', '0049758334', 'Lawrence Radja Setyanugraha', '10'),
(191, 'A24.278', '0068466264', 'Kevin Farrel Awanta Fausta', '10'),
(192, 'A24.277', '0069794422', 'Joseph Michael Bagaskara Simamora', '10'),
(193, 'A24.276', '0069425001', 'Fransiskus Brayen Caprico Viliano', '10'),
(194, 'A24.275', '0065895217', 'Febrian Satya Prianggono', '10'),
(195, 'A24.274', '0067271119', 'Bonfilius Monaldio Hernandson Wicaksana', '10'),
(196, 'A24.273', '0091712929', 'Benediktus Bernardin Wirayuda Asmara', '10'),
(197, 'A24.271', '0063223287', 'Benedicto Unggul Deva Immanuel', '10'),
(198, 'A24.272', '0065453196', 'Benedictus Briliantara', '10'),
(199, 'A24.280', '0040775508', 'Martinus Bryan Aprilianto', '10'),
(200, 'A24.281', '0064291330', 'Michael Bryan Ardhitama', '10'),
(201, 'A24.282', '0061838023', 'Natan Cahyo Adi Putranto', '10'),
(202, 'A24.283', '0050858283', 'Patrick Pancapani', '10'),
(203, 'A24.284', '0067740338', 'Valentino Minguel Siahaya', '10'),
(204, 'P24.1629', '0097448325', 'Aloysius Karel Wicaksono', '1'),
(205, 'P24.1630', '0081737144', 'Anselmus Pio Pazia Serafino', '1'),
(206, 'P24.1632', '0096318853', 'Aurelius Benton Filistin Tampubolon', '1'),
(207, 'P24.1633', '0099739855', 'Benedictus Dimas Bagus Widyaprasetya', '1'),
(208, 'P24.1635', '0088231391', 'Benediktus Nathan Dama Cahyadi', '1'),
(209, 'P24.1636', '0081017230', 'Benekditus Novel Hermawan', '1'),
(210, 'P24.1643', '0089121291', 'Franciscus Asisi Novendhika Yulianto', '1'),
(211, 'P24.1646', '0093318160', 'Gabriel Wicaksono Satyawira', '1'),
(212, 'P24.1648', '0096829280', 'Giovanni Arif Budi Santoso', '1'),
(213, 'P24.1649', '0094712196', 'Gregorius Arga Maheswara', '1'),
(214, 'P24.1657', '0084209014', 'Jordan Dos Prestigio Dandy Setiawan', '1'),
(215, 'P24.1659', '0093924243', 'Kanisius Nazareth Date', '1'),
(216, 'P24.1660', '0097968881', 'Krisna Radityatama', '1'),
(217, 'P24.1661', '0087976591', 'Kurniawan Respati Istyastono', '1'),
(218, 'P24.1663', '0094493329', 'Maximus Vito Masartiva', '1'),
(219, 'P24.1668', '0091281566', 'Mikhael Inggit Rentius Bayu Sadewa', '1'),
(220, 'P24.1671', '0093717656', 'Nathanael Pratama', '1'),
(221, 'P24.1673', '0092522302', 'Pradipta Bhadra Paramadiwa', '1'),
(222, 'P24.1674', '0094927437', 'Rafael Ludwig Yoga Agastya', '1'),
(223, 'P24.1680', '0087535324', 'Theovillus Nathan Yundra Dwi Saputra', '1'),
(224, 'P24.1631', '0096051387', 'Antonius Jefferi Kurniawan', '2'),
(225, 'P24.1639', '0093093522', 'Dimitrij Gana Narotama', '2'),
(226, 'P24.1642', '0096120344', 'Francesco Deven Berwin Sinaga', '2'),
(227, 'P24.1644', '0094640147', 'Fransiskus Sangaji Darma Songga', '2'),
(228, 'P24.1645', '0088852653', 'Gabriel Arvin Honesto', '2'),
(229, 'P24.1647', '0093091685', 'Georgius Christian Bagus Andhanu', '2'),
(230, 'P24.1651', '0094875018', 'Hugo Ryozka Jalu Wicaksana', '2'),
(231, 'P24.1654', '0089120457', 'Jati Kharisma Cendana', '2'),
(232, 'P24.1656', '0095015476', 'Johanes Valentino Thomas', '2'),
(233, 'P24.1662', '0094854588', 'Matias Nathan Erko Pradana', '2'),
(234, 'P24.1665', '0098289145', 'Michael Raditya Setiaji', '2'),
(235, 'P24.1666', '0083639613', 'Michael Rendy Pratama', '2'),
(236, 'P24.1667', '0098551817', 'Miguel Albert Andes', '2'),
(237, 'P24.1670', '0096122421', 'Nathanael Arakenta', '2'),
(238, 'P24.1672', '0085548815', 'Petrus Canecius Adveno Jonatan', '2'),
(239, 'P24.1677', '0093398151', 'Savio Galgeni Sigit', '2'),
(240, 'P24.1678', '0098941263', 'Servatius Nayaka Adyandaru', '2'),
(241, 'P24.1681', '0095074692', 'Timotius Hibran Adi Wibawanta', '2'),
(242, 'P24.1682', '0099111992', 'Valentino Rangga Ikip Pramudya', '2'),
(243, 'P24.1627', '0081459781', 'Adito Putra Indarto', '3'),
(244, 'P24.1628', '0091535155', 'Agatho Yoanes Maria Vianney Salmon', '3'),
(245, 'P24.1634', '0096537541', 'Benediktus Catur Sakti', '3'),
(246, 'P24.1637', '0091712929', 'Bernardus Arthur Setiawan', '3'),
(247, 'P24.1638', '0087560202', 'Christiano Jason Lemuel Wijaya', '3'),
(248, 'P24.1640', '0089418994', 'Dionisius Oktavia Atmadja', '3'),
(249, 'P24.1641', '0085339837', 'Evodius Ra Kei Imago Dei', '3'),
(250, 'P24.1650', '0095829045', 'Hieronymus Manuel Hadrian Kurniawan', '3'),
(251, 'P24.1652', '0095618217', 'Ignasius Budya Archanadarma', '3'),
(252, 'P24.1653', '0077300411', 'Imanuel Jerindo', '3'),
(253, 'P24.1655', '0099165066', 'Johanes Bramandiharto', '3'),
(254, 'P24.1658', '0081016372', 'Kalistus Andhika Krisnayana Ajibrata', '3'),
(255, 'P24.1664', '0094448086', 'Michael Evan Bagas Ardyanta', '3'),
(256, 'P24.1669', '0092943538', 'Mikhael Svendranosius Ananta', '3'),
(257, 'P24.1675', '0097618895', 'Raymundus Pandu Setyaji', '3'),
(258, 'P24.1676', '0093269511', 'Redemptus Damar Adi Nugraha', '3'),
(259, 'P24.1679', '0085635020', 'Sistus Wikassadharma', '3'),
(260, 'P24.1683', '0098495060', 'Vicentius Rio Kristian Bara', '3'),
(261, 'P24.1684', '0091005184', 'Yohanes Paulus Dewa Made Mazmur Nusantara Raya', '3');

--
-- Indexes for dumped tables
--

--
-- Indeks untuk tabel `kelas`
--
ALTER TABLE `kelas`
  ADD PRIMARY KEY (`id_kelas`);

--
-- Indeks untuk tabel `peminjaman`
--
ALTER TABLE `peminjaman`
  ADD PRIMARY KEY (`id_peminjaman`);

--
-- Indeks untuk tabel `pengambilan_atk`
--
ALTER TABLE `pengambilan_atk`
  ADD PRIMARY KEY (`id_pengambilan_atk`);

--
-- Indeks untuk tabel `siswa`
--
ALTER TABLE `siswa`
  ADD PRIMARY KEY (`id_siswa`);

--
-- AUTO_INCREMENT untuk tabel yang dibuang
--

--
-- AUTO_INCREMENT untuk tabel `kelas`
--
ALTER TABLE `kelas`
  MODIFY `id_kelas` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT untuk tabel `peminjaman`
--
ALTER TABLE `peminjaman`
  MODIFY `id_peminjaman` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=85;

--
-- AUTO_INCREMENT untuk tabel `pengambilan_atk`
--
ALTER TABLE `pengambilan_atk`
  MODIFY `id_pengambilan_atk` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=94;

--
-- AUTO_INCREMENT untuk tabel `siswa`
--
ALTER TABLE `siswa`
  MODIFY `id_siswa` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=262;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
