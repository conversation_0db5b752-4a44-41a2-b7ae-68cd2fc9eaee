<?php
// Test koneksi database dan cek tabel tahun_ajaran
try {
    $host = 'localhost';
    $dbname = 'peminjaman2';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Koneksi database berhasil!\n\n";
    
    // Cek apakah tabel tahun_ajaran ada
    $stmt = $pdo->query("SHOW TABLES LIKE 'tahun_ajaran'");
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "✅ Tabel tahun_ajaran sudah ada!\n\n";
        
        // Cek struktur tabel
        echo "📋 Struktur tabel tahun_ajaran:\n";
        $stmt = $pdo->query("DESCRIBE tahun_ajaran");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
        
        echo "\n📊 Data dalam tabel:\n";
        $stmt = $pdo->query("SELECT * FROM tahun_ajaran ORDER BY tahun_ajaran, semester");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($data) > 0) {
            foreach ($data as $row) {
                echo "ID: {$row['id_tahun_ajaran']}, Tahun: {$row['tahun_ajaran']}, Semester: {$row['semester']}, Aktif: " . ($row['is_active'] ? 'Ya' : 'Tidak') . "\n";
            }
        } else {
            echo "Tidak ada data dalam tabel.\n";
        }
        
    } else {
        echo "❌ Tabel tahun_ajaran belum ada!\n";
        echo "Silakan jalankan script fix_database_tahun_ajaran.sql di phpMyAdmin.\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error koneksi database: " . $e->getMessage() . "\n";
}
?>
