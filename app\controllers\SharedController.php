<?php 

/**
 * SharedController Controller
 * @category  Controller / Model
 */
class SharedController extends BaseController{
	
	/**
     * peminjaman_kelas_option_list Model Action
     * @return array
     */
	function peminjaman_kelas_option_list(){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_kelas AS value,nama_kelas AS label FROM kelas";
		$queryparams = null;
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

	/**
     * peminjaman_nama_option_list Model Action
     * @return array
     */
	function peminjaman_nama_option_list($lookup_kelas){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_siswa AS value,nama_siswa AS label FROM siswa WHERE id_kelas= ? ORDER BY nama_siswa ASC" ;
		$queryparams = array($lookup_kelas);
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

	/**
     * pengambilan_atk_kelas_option_list Model Action
     * @return array
     */
	function pengambilan_atk_kelas_option_list(){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_kelas AS value,nama_kelas AS label FROM kelas";
		$queryparams = null;
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

	/**
     * pengambilan_atk_nama_option_list Model Action
     * @return array
     */
	function pengambilan_atk_nama_option_list($lookup_kelas){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_siswa AS value,nama_siswa AS label FROM siswa WHERE id_kelas= ? ORDER BY nama_siswa ASC" ;
		$queryparams = array($lookup_kelas);
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

	/**
     * siswa_nama_siswa_option_list Model Action
     * @return array
     */
	function siswa_nama_siswa_option_list(){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_siswa AS value,nama_siswa AS label FROM siswa";
		$queryparams = null;
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

	/**
     * siswa_id_kelas_option_list Model Action
     * @return array
     */
	function siswa_id_kelas_option_list(){
		$db = $this->GetModel();
		$sqltext = "SELECT  DISTINCT id_kelas AS value,nama_kelas AS label FROM kelas";
		$queryparams = null;
		$arr = $db->rawQuery($sqltext, $queryparams);
		return $arr;
	}

}
