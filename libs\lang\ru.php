<?php

return array(
    'validate_required'                 => 'Поле {field} является обязательным',
    'validate_valid_email'              => 'Поле {field} должно быть Email адресом',
    'validate_max_len'                  => 'Поле {field} должно быть максимум {param} символов',
    'validate_min_len'                  => 'Поле {field} должно быть минимум {param} символов',
    'validate_exact_len'                => 'Поле {field} должно содержать ровно {param} символов',
    'validate_alpha'                    => 'Поле {field} может содержать только буквы',
    'validate_alpha_numeric'            => 'Поле {field} может содержать только буквы и цифры',
    'validate_alpha_numeric_space'      => 'Поле {field} может содержать только буквы, цифры и пробелы',
    'validate_alpha_dash'               => 'Поле {field} может содержать только буквы и дефис',
    'validate_alpha_space'              => 'Поле {field} может содержать только буквы и пробел',
    'validate_numeric'                  => 'Поле {field} должно быть числом',
    'validate_integer'                  => 'Поле {field} должно быть целым числом',
    'validate_boolean'                  => 'Поле {field} должно быть true или false',
    'validate_float'                    => 'Поле {field} должно быть не целым числом',
    'validate_valid_url'                => 'Поле {field} должно быть ссылкой',
    'validate_url_exists'               => 'Ссылка {field} не доступна',
    'validate_valid_ip'                 => 'Поле {field} должно быть IP адресом',
    'validate_valid_ipv4'               => 'Поле {field} должно быть IPv4 адресом',
    'validate_valid_ipv6'               => 'Поле {field} должно быть IPv6 адресом',
    'validate_guidv4'                   => 'Поле {field} должно быть GUID',
    'validate_valid_cc'                 => 'Номер карты {field} не является валидным',
    'validate_valid_name'               => 'Поле {field} должно содержать полное имя',
    'validate_contains'                 => 'Поле {field} может содержать следующие значения: {param}',
    'validate_contains_list'            => 'Значение {field} не может быть использовано как ответ',
    'validate_doesnt_contain_list'      => 'Значение {field} не может быть использовано',
    'validate_street_address'           => 'Поле {field} должно быть адресом',
    'validate_date'                     => 'Поле {field} должно быть датой',
    'validate_min_numeric'              => 'Поле {field} должно быть числом не менее {param}',
    'validate_max_numeric'              => 'Поле {field} должно быть числом не более {param}',
    'validate_min_age'                  => 'Возраст должен быть более {param}',
    'validate_invalid'                  => 'Поле {field} не прошло проверку',
    'validate_starts'                   => 'Поле {field} должно начинаться {param}',
    'validate_extension'                => 'Файл {field} должен быть следующим типом: {param}',
    'validate_required_file'            => 'Файл {field} обязателен к загрузке',
    'validate_equalsfield'              => 'Поле {field} должно быть идентичным {param}',
    'validate_iban'                     => 'Поле {field} должно быть правильным IBAN',
    'validate_phone_number'             => 'Поле {field} должно быть правильным номером телефона',
    'validate_regex'                    => 'Поле {field} должно содержать правильное значение',
    'validate_valid_json_string'        => 'Поле {field} должно быть валидной JSON строкой',
    'validate_valid_array_size_greater' => 'Поле {field} должно содержать минимум {param} значений',
    'validate_valid_array_size_lesser'  => 'Поле {field} должно содержать максимум {param} значений',
    'validate_valid_array_size_equal'   => 'Поле {field} должно содержать ровно {param} значений',
);
