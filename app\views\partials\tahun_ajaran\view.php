<?php 
$page_id = null;
$comp_model = new SharedController;
$current_page = $this->set_current_page_link();
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fa fa-eye"></i> Detail Tahun Ajaran</h4>
                </div>
                <div class="card-body">
                    <?php $this->display_page_errors(); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Tahun Ajaran:</strong></label>
                                <p class="form-control-plaintext"><?php echo isset($data['tahun_ajaran']) ? $data['tahun_ajaran'] : '-'; ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Semester:</strong></label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-<?php echo (isset($data['semester']) && $data['semester'] == '1') ? 'primary' : 'warning'; ?>">
                                        Semester <?php echo isset($data['semester']) ? $data['semester'] : '-'; ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Tanggal Mulai:</strong></label>
                                <p class="form-control-plaintext"><?php echo isset($data['tanggal_mulai']) ? $data['tanggal_mulai'] : '-'; ?></p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label><strong>Tanggal Selesai:</strong></label>
                                <p class="form-control-plaintext"><?php echo isset($data['tanggal_selesai']) ? $data['tanggal_selesai'] : '-'; ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label><strong>Status:</strong></label>
                        <p class="form-control-plaintext">
                            <span class="badge badge-<?php echo (isset($data['is_active']) && $data['is_active']) ? 'success' : 'secondary'; ?>">
                                <?php echo isset($data['status_text']) ? $data['status_text'] : 'Tidak Aktif'; ?>
                                <?php if(isset($data['is_active']) && $data['is_active']): ?>
                                    <i class="fa fa-check-circle"></i>
                                <?php endif; ?>
                            </span>
                        </p>
                    </div>
                    
                    <?php if(isset($data['keterangan']) && !empty($data['keterangan'])): ?>
                    <div class="form-group">
                        <label><strong>Keterangan:</strong></label>
                        <p class="form-control-plaintext"><?php echo nl2br(htmlspecialchars($data['keterangan'])); ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label><strong>Dibuat:</strong></label>
                        <p class="form-control-plaintext">
                            <small class="text-muted">
                                <?php echo isset($data['created_at']) ? date('d-m-Y H:i', strtotime($data['created_at'])) : '-'; ?>
                            </small>
                        </p>
                    </div>

                    <?php if(isset($data['updated_at']) && isset($data['created_at']) && $data['updated_at'] != $data['created_at']): ?>
                    <div class="form-group">
                        <label><strong>Terakhir Diupdate:</strong></label>
                        <p class="form-control-plaintext">
                            <small class="text-muted">
                                <?php echo date('d-m-Y H:i', strtotime($data['updated_at'])); ?>
                            </small>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="form-group mt-4">
                        <?php if(isset($data['id_tahun_ajaran'])): ?>
                        <a href="<?php print_link('tahun_ajaran/edit/' . $data['id_tahun_ajaran']); ?>" class="btn btn-warning">
                            <i class="fa fa-edit"></i> Edit
                        </a>

                        <?php if(isset($data['is_active']) && !$data['is_active']): ?>
                        <a href="<?php print_link('tahun_ajaran/set_active/' . $data['id_tahun_ajaran']); ?>"
                           class="btn btn-success"
                           onclick="return confirm('Set tahun ajaran ini sebagai aktif?')">
                            <i class="fa fa-check"></i> Set Aktif
                        </a>
                        <?php endif; ?>

                        <a href="<?php print_link('tahun_ajaran/delete/' . $data['id_tahun_ajaran']); ?>"
                           class="btn btn-danger"
                           onclick="return confirm('Yakin ingin menghapus tahun ajaran ini?')">
                            <i class="fa fa-trash"></i> Hapus
                        </a>
                        <?php endif; ?>
                        
                        <a href="<?php print_link('tahun_ajaran'); ?>" class="btn btn-secondary">
                            <i class="fa fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
