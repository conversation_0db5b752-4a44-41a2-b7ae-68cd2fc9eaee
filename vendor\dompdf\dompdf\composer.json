{"name": "dompdf/dompdf", "type": "library", "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "license": "LGPL-2.1", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "require": {"php": ">=5.4.0", "ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "0.5.*", "phenx/php-svg-lib": "0.3.*"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"ext-gd": "Needed to process images", "ext-imagick": "Improves image processing performance", "ext-gmagick": "Improves image processing performance"}, "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}}