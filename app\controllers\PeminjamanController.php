<?php 
/**
 * Peminjaman Page Controller
 * @category  Controller
 */
class PeminjamanController extends BaseController{
	function __construct(){
		parent::__construct();
		$this->tablename = "peminjaman";
	}
	/**
     * List page records
     * @param $fieldname (filter record by a field) 
     * @param $fieldvalue (filter field value)
     * @return BaseView
     */
	function index($fieldname = null , $fieldvalue = null){
		$request = $this->request;
		$db = $this->GetModel();
		$tablename = $this->tablename;
		$fields = array("peminjaman.id_peminjaman", 
			"peminjaman.tanggal_pinjam", 
			"peminjaman.nama", 
			"siswa.nama_siswa AS siswa_nama_siswa", 
			"peminjaman.kelas", 
			"kelas.nama_kelas AS kelas_nama_kelas", 
			"peminjaman.barang", 
			"peminjaman.tanggal_kembali", 
			"peminjaman.kegiatan");
		$pagination = $this->get_pagination(MAX_RECORD_COUNT); // get current pagination e.g array(page_number, page_limit)
		//search table record
		if(!empty($request->search)){
			$text = trim($request->search); 
			$search_condition = "(
				peminjaman.id_peminjaman LIKE ? OR 
				peminjaman.tanggal_pinjam LIKE ? OR 
				peminjaman.nama LIKE ? OR 
				peminjaman.kelas LIKE ? OR 
				peminjaman.barang LIKE ? OR 
				peminjaman.tanggal_kembali LIKE ? OR 
				peminjaman.kegiatan LIKE ?
			)";
			$search_params = array(
				"%$text%","%$text%","%$text%","%$text%","%$text%","%$text%","%$text%"
			);
			//setting search conditions
			$db->where($search_condition, $search_params);
			 //template to use when ajax search
			$this->view->search_template = "peminjaman/search.php";
		}
		$db->join("siswa", "peminjaman.nama = siswa.id_siswa", "INNER");
		$db->join("kelas", "peminjaman.kelas = kelas.id_kelas", "INNER");
		if(!empty($request->orderby)){
			$orderby = $request->orderby;
			$ordertype = (!empty($request->ordertype) ? $request->ordertype : ORDER_TYPE);
			$db->orderBy($orderby, $ordertype);
		}
		else{
			$db->orderBy("peminjaman.id_peminjaman", ORDER_TYPE);
		}
		if($fieldname){
			$db->where($fieldname , $fieldvalue); //filter by a single field name
		}
		$tc = $db->withTotalCount();
		$records = $db->get($tablename, $pagination, $fields);
		$records_count = count($records);
		$total_records = intval($tc->totalCount);
		$page_limit = $pagination[1];
		$total_pages = ceil($total_records / $page_limit);
		if(	!empty($records)){
			foreach($records as &$record){
				$record['tanggal_pinjam'] = format_date($record['tanggal_pinjam'],'d-m-Y');
$record['tanggal_kembali'] = format_date($record['tanggal_kembali'],'d-m-Y');
			}
		}
		$data = new stdClass;
		$data->records = $records;
		$data->record_count = $records_count;
		$data->total_records = $total_records;
		$data->total_page = $total_pages;
		if($db->getLastError()){
			$this->set_page_error();
		}
		$page_title = $this->view->page_title = "Peminjaman";
		$this->view->report_filename = date('Y-m-d') . '-' . $page_title;
		$this->view->report_title = $page_title;
		$this->view->report_layout = "report_layout.php";
		$this->view->report_paper_size = "A4";
		$this->view->report_orientation = "portrait";
		$this->render_view("peminjaman/list.php", $data); //render the full page
	}
	/**
     * View record detail 
	 * @param $rec_id (select record by table primary key) 
     * @param $value value (select record by value of field name(rec_id))
     * @return BaseView
     */
	function view($rec_id = null, $value = null){
		$request = $this->request;
		$db = $this->GetModel();
		$rec_id = $this->rec_id = urldecode($rec_id);
		$tablename = $this->tablename;
		$fields = array("peminjaman.id_peminjaman", 
			"peminjaman.tanggal_pinjam", 
			"peminjaman.nama", 
			"siswa.nama_siswa AS siswa_nama_siswa", 
			"peminjaman.kelas", 
			"kelas.nama_kelas AS kelas_nama_kelas", 
			"peminjaman.barang", 
			"peminjaman.tanggal_kembali", 
			"peminjaman.kegiatan");
		if($value){
			$db->where($rec_id, urldecode($value)); //select record based on field name
		}
		else{
			$db->where("peminjaman.id_peminjaman", $rec_id);; //select record based on primary key
		}
		$db->join("siswa", "peminjaman.nama = siswa.id_siswa", "INNER");
		$db->join("kelas", "peminjaman.kelas = kelas.id_kelas", "INNER");  
		$record = $db->getOne($tablename, $fields );
		if($record){
			$record['tanggal_pinjam'] = format_date($record['tanggal_pinjam'],'d-m-Y');
$record['tanggal_kembali'] = format_date($record['tanggal_kembali'],'d-m-Y');
			$page_title = $this->view->page_title = "View  Peminjaman";
		$this->view->report_filename = date('Y-m-d') . '-' . $page_title;
		$this->view->report_title = $page_title;
		$this->view->report_layout = "report_layout.php";
		$this->view->report_paper_size = "A4";
		$this->view->report_orientation = "portrait";
		}
		else{
			if($db->getLastError()){
				$this->set_page_error();
			}
			else{
				$this->set_page_error("No record found");
			}
		}
		return $this->render_view("peminjaman/view.php", $record);
	}
	/**
     * Insert new record to the database table
	 * @param $formdata array() from $_POST
     * @return BaseView
     */
	function add($formdata = null){
		if($formdata){
			$db = $this->GetModel();
			$tablename = $this->tablename;
			$request = $this->request;
			//fillable fields
			$fields = $this->fields = array("tanggal_pinjam","kelas","nama","barang","kegiatan","tanggal_kembali");
			$postdata = $this->format_request_data($formdata);
			$this->rules_array = array(
				'tanggal_pinjam' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'barang' => 'required',
				'kegiatan' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal_pinjam' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'barang' => 'sanitize_string',
				'kegiatan' => 'sanitize_string',
				'tanggal_kembali' => 'sanitize_string',
			);
			$this->filter_vals = true; //set whether to remove empty fields
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$rec_id = $this->rec_id = $db->insert($tablename, $modeldata);
				if($rec_id){
					$this->set_flash_msg("Record added successfully", "success");
					return	$this->redirect("peminjaman");
				}
				else{
					$this->set_page_error();
				}
			}
		}
		$page_title = $this->view->page_title = "Add New Peminjaman";
		$this->render_view("peminjaman/add.php");
	}
	/**
     * Update table record with formdata
	 * @param $rec_id (select record by table primary key)
	 * @param $formdata array() from $_POST
     * @return array
     */
	function edit($rec_id = null, $formdata = null){
		$request = $this->request;
		$db = $this->GetModel();
		$this->rec_id = $rec_id;
		$tablename = $this->tablename;
		 //editable fields
		$fields = $this->fields = array("id_peminjaman","tanggal_pinjam","kelas","nama","barang","kegiatan","tanggal_kembali");
		if($formdata){
			$postdata = $this->format_request_data($formdata);
			$this->rules_array = array(
				'tanggal_pinjam' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'barang' => 'required',
				'kegiatan' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal_pinjam' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'barang' => 'sanitize_string',
				'kegiatan' => 'sanitize_string',
				'tanggal_kembali' => 'sanitize_string',
			);
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$db->where("peminjaman.id_peminjaman", $rec_id);;
				$bool = $db->update($tablename, $modeldata);
				$numRows = $db->getRowCount(); //number of affected rows. 0 = no record field updated
				if($bool && $numRows){
					$this->set_flash_msg("Record updated successfully", "success");
					return $this->redirect("peminjaman");
				}
				else{
					if($db->getLastError()){
						$this->set_page_error();
					}
					elseif(!$numRows){
						//not an error, but no record was updated
						$page_error = "No record updated";
						$this->set_page_error($page_error);
						$this->set_flash_msg($page_error, "warning");
						return	$this->redirect("peminjaman");
					}
				}
			}
		}
		$db->where("peminjaman.id_peminjaman", $rec_id);;
		$data = $db->getOne($tablename, $fields);
		$page_title = $this->view->page_title = "Edit  Peminjaman";
		if(!$data){
			$this->set_page_error();
		}
		return $this->render_view("peminjaman/edit.php", $data);
	}
	/**
     * Update single field
	 * @param $rec_id (select record by table primary key)
	 * @param $formdata array() from $_POST
     * @return array
     */
	function editfield($rec_id = null, $formdata = null){
		$db = $this->GetModel();
		$this->rec_id = $rec_id;
		$tablename = $this->tablename;
		//editable fields
		$fields = $this->fields = array("id_peminjaman","tanggal_pinjam","kelas","nama","barang","kegiatan","tanggal_kembali");
		$page_error = null;
		if($formdata){
			$postdata = array();
			$fieldname = $formdata['name'];
			$fieldvalue = $formdata['value'];
			$postdata[$fieldname] = $fieldvalue;
			$postdata = $this->format_request_data($postdata);
			$this->rules_array = array(
				'tanggal_pinjam' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'barang' => 'required',
				'kegiatan' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal_pinjam' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'barang' => 'sanitize_string',
				'kegiatan' => 'sanitize_string',
				'tanggal_kembali' => 'sanitize_string',
			);
			$this->filter_rules = true; //filter validation rules by excluding fields not in the formdata
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$db->where("peminjaman.id_peminjaman", $rec_id);;
				$bool = $db->update($tablename, $modeldata);
				$numRows = $db->getRowCount();
				if($bool && $numRows){
					return render_json(
						array(
							'num_rows' =>$numRows,
							'rec_id' =>$rec_id,
						)
					);
				}
				else{
					if($db->getLastError()){
						$page_error = $db->getLastError();
					}
					elseif(!$numRows){
						$page_error = "No record updated";
					}
					render_error($page_error);
				}
			}
			else{
				render_error($this->view->page_error);
			}
		}
		return null;
	}
	/**
     * Delete record from the database
	 * Support multi delete by separating record id by comma.
     * @return BaseView
     */
	function delete($rec_id = null){
		Csrf::cross_check();
		$request = $this->request;
		$db = $this->GetModel();
		$tablename = $this->tablename;
		$this->rec_id = $rec_id;
		//form multiple delete, split record id separated by comma into array
		$arr_rec_id = array_map('trim', explode(",", $rec_id));
		$db->where("peminjaman.id_peminjaman", $arr_rec_id, "in");
		$bool = $db->delete($tablename);
		if($bool){
			$this->set_flash_msg("Record deleted successfully", "success");
		}
		elseif($db->getLastError()){
			$page_error = $db->getLastError();
			$this->set_flash_msg($page_error, "danger");
		}
		return	$this->redirect("peminjaman");
	}
}
