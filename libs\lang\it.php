<?php

return array(
    'validate_required'                 => 'Il campo {field} è obbligatorio',
    'validate_valid_email'              => 'Il campo {field} deve essere un indirizzo email valido',
    'validate_max_len'                  => 'Il campo {field} deve essere di {param} caratteri o inferiore',
    'validate_min_len'                  => 'Il campo {field} deve essere di almeno {param} caratteri',
    'validate_exact_len'                => 'Il campo {field} deve essere di {param} caratteri esatti',
    'validate_alpha'                    => 'Il campo {field} deve contenere solo lettere',
    'validate_alpha_numeric'            => 'Il campo {field} può contenere solo lettere e numeri',
    'validate_alpha_numeric_space'      => 'Il campo {field} può contenere solo lettere, numeri e spazi',
    'validate_alpha_dash'               => 'Il campo {field} può contenere solo lettere e trattini',
    'validate_alpha_space'              => 'Il campo {field} può contenere solo lettere e spazi',
    'validate_numeric'                  => 'Il campo {field} deve essere un numero',
    'validate_integer'                  => 'Il campo {field} deve essere un numero senza virgola',
    'validate_boolean'                  => 'Il campo {field} deve essere vero o falso',
    'validate_float'                    => 'Il campo {field} deve essere un numero con almeno un numero dopo la virgola',
    'validate_valid_url'                => 'Il campo {field} deve essere un URL',
    'validate_url_exists'               => 'L\'URL {field} non esiste',
    'validate_valid_ip'                 => 'Il campo {field} deve essere un indirizzo IP valido',
    'validate_valid_ipv4'               => 'Il campo {field} deve contenere un indirizzo IPv4 valido',
    'validate_valid_ipv6'               => 'Il campo {field} deve contenere un indirizzo IPv6 valido',
    'validate_guidv4'                   => 'Il campo {field} deve contenere un GUID valido',
    'validate_valid_cc'                 => 'Il campo {field} non è un numero di carta di credito valido',
    'validate_valid_name'               => 'Il campo {field} deve contenere un nome completo',
    'validate_contains'                 => 'Il campo {field} può contenere solo uno dei seguenti valori: {param}',
    'validate_contains_list'            => 'Il campo {field} non è un\'opzione valida',
    'validate_doesnt_contain_list'      => 'Il campo {field} contiene un valore che non è accettato',
    'validate_street_address'           => 'Il campo {field} deve contenere un indirizzo valido',
    'validate_date'                     => 'Il campo {field} deve contenere una data valida',
    'validate_min_numeric'              => 'Il campo {field} deve contenere un valore numerico maggiore o uguale a {param}',
    'validate_max_numeric'              => 'Il campo {field} deve contenere un valore numerico minore o uguale a {param}',
    'validate_min_age'                  => 'Il campo {field} deve contenere un\'età maggiore o uguale a {param}',
    'validate_invalid'                  => 'Il campo {field} contiene un valore non valido',
    'validate_starts'                   => 'Il campo {field} deve cominciare con {param}',
    'validate_extension'                => 'Il campo {field} può avere solo le seguenti estensioni: {param}',
    'validate_required_file'            => 'Il campo {field} è obbligatorio',
    'validate_equalsfield'              => 'Il campo {field} non è uguale al campo {param}',
    'validate_iban'                     => 'Il campo {field} non contiene un IBAN valido',
    'validate_phone_number'             => 'Il campo {field} deve contenere un numero di telefono valido',
    'validate_regex'                    => 'Il campo {field} deve contenere un valore in un formato valido',
    'validate_valid_json_string'        => 'Il campo {field} deve contenere una stringa in formato JSON corretto',
    'validate_valid_array_size_greater' => 'Il campo {field} deve essere un array di dimensioni maggiori o uguali a {param}',
    'validate_valid_array_size_lesser'  => 'Il campo {field} deve essere un array di dimensioni minori o uguali a {param}',
    'validate_valid_array_size_equal'   => 'Il campo {field} deve essere un array di dimensioni uguali a {param}'
);