<?php

return array(
    'validate_required'             => 'Le champ {field} est obligatoire',
    'validate_valid_email'          => 'Le champ {field} doit &#234;tre un email valide',
    'validate_max_len'              => 'Le champ {field} doit avoir un nombre de caract&#232;re de {param} ou moins',
    'validate_min_len'              => 'Le champ {field} doit avoir un nombre de caract&#232;re de {param} ou plus',
    'validate_exact_len'            => 'Le champ {field} doit avoir un nombre de caract&#232;re de {param}',
    'validate_alpha'                => 'Le champ {field} doit seulement contenir des caract&#232;res alpha (a-z)',
    'validate_alpha_numeric'        => 'Le champ {field} doit seulement contenir des caract&#232;res alpha-num&#233;rique (a-z)',
    'validate_alpha_dash'           => 'Le champ {field} doit seulement contenir des caract&#232;res alpha (a-z) et tiret',
    'validate_alpha_space'          => 'Le champ {field} doit seulement contenir des caract&#232;res alpha (a-z) et espace',
    'validate_numeric'              => 'Le champ {field} doit seulement contenir des caract&#232;res num&#233;riques',
    'validate_integer'              => 'Le champ {field} doit &#234;tre une valeur num&#233;rique',
    'validate_boolean'              => 'Le champ {field} doit &#234;tre vrai ou faux',
    'validate_float'                => 'Le champ {field} doit &#234;tre une valeur d&#233;cimale',
    'validate_valid_url'            => 'Le champ {field} doit &#234;tre une URL valide',
    'validate_url_exists'           => 'L&#44;URL {field} n&#44;existe pas',
    'validate_valid_ip'             => 'Le champ {field} doit contenir une adresse IP valide',
    'validate_valid_ipv4'           => 'Le champ {field} doit contenir une adresse IPv4 valide',
    'validate_valid_ipv6'           => 'Le champ {field} doit contenir une adresse IPv6 valide',
    'validate_guidv4'               => 'Le champ {field} doit contenir un GUID valide',
    'validate_valid_cc'             => 'Le champ {field} doit contenir un num&#233;ro de carte bancaire valide',
    'validate_valid_name'           => 'Le champ {field} doit contenir un nom humain valide',
    'validate_contains'             => 'Le champ {field} doit contenir une des ces valeurs: {param}',
    'validate_contains_list'        => 'Le champ {field} doit contenir une valeur du menu d&#233;roulant',
    'validate_doesnt_contain_list'  => 'Le champ {field} contient une valeur qui n&#44;est pas acceptable',
    'validate_street_address'       => 'Le champ {field} doit &#234;tre une adresse postale valide',
    'validate_date'                 => 'Le champ {field} doit &#234;tre une date valide',
    'validate_min_numeric'          => 'Le champ {field} doit &#234;tre une valeur num&#233;rique &#233;gale ou sup&#233;rieur à {param}',
    'validate_max_numeric'          => 'Le champ {field} doit &#234;tre une valeur num&#233;rique &#233;gale ou inf&#233;rieur à {param}',
    'validate_min_age'              => 'Le champ {field} doit &#234;tre un &#226;ge &#233;gal ou sup&#233;rieur à {param}',
    'validate_invalid'              => 'Le champ {field} est invalide',
    'validate_starts'               => 'Le champ {field} doit commencer par {param}',
    'validate_extension'            => 'Le champ {field} doit avoir les extensions suivantes {param}',
    'validate_required_file'        => 'Le champ {field} est obligatoire',
    'validate_equalsfield'          => 'Le champ {field} n&#44;est pas &#233;gale au champ {param}',
    'validate_iban'                 => 'Le champ {field} doit contenir un IBAN valide',
    'validate_phone_number'         => 'Le champ {field} doit contenir un num&#233;ro de t&#233;l&#233;phone valide',
    'validate_regex'                => 'Le champ {field} doit contenir une valeur valide',
    'validate_valid_json_string'    => 'Le champ {field} doit avoir un format JSON',
);
