<?php
$page_id = null;
$comp_model = new SharedController;
$current_page = $this->set_current_page_link();

// Get dashboard statistics
$db = new PDODb(DB_TYPE, DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME, DB_PORT, DB_CHARSET);

// Total peminjaman hari ini
$db->where("DATE(tanggal_pinjam)", date('Y-m-d'));
$peminjaman_hari_ini = $db->getValue("peminjaman", "COUNT(*)");

// Total peminjaman yang belum dikembalikan (tanggal_kembali = 0000-00-00 atau kosong)
$db->where("(tanggal_kembali = '0000-00-00' OR tanggal_kembali IS NULL)");
$belum_kembali = $db->getValue("peminjaman", "COUNT(*)");

// Total pengambilan ATK hari ini
$db->where("DATE(tanggal)", date('Y-m-d'));
$atk_hari_ini = $db->getValue("pengambilan_atk", "COUNT(*)");

// Total siswa
$total_siswa = $db->getValue("siswa", "COUNT(*)");

// Peminjaman yang terlambat (tanggal kembali sudah lewat tapi belum dikembalikan)
$db->where("tanggal_kembali < CURDATE()");
$db->where("tanggal_kembali != '0000-00-00'");
$db->where("tanggal_kembali IS NOT NULL");
$terlambat = $db->getValue("peminjaman", "COUNT(*)");

// Recent activities - 5 peminjaman terbaru
$recent_peminjaman = $db->rawQuery("
    SELECT p.*, s.nama_siswa, k.nama_kelas
    FROM peminjaman p
    LEFT JOIN siswa s ON p.nama = s.id_siswa
    LEFT JOIN kelas k ON p.kelas = k.id_kelas
    ORDER BY p.id_peminjaman DESC
    LIMIT 5
");

// Recent ATK - 5 pengambilan terbaru
$recent_atk = $db->rawQuery("
    SELECT pa.*, s.nama_siswa, k.nama_kelas
    FROM pengambilan_atk pa
    LEFT JOIN siswa s ON pa.nama = s.id_siswa
    LEFT JOIN kelas k ON pa.kelas = k.id_kelas
    ORDER BY pa.id_pengambilan_atk DESC
    LIMIT 5
");
?>
<div>
    <!-- Header -->
    <div class="bg-primary text-white p-4 mb-4">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-1">Dashboard</h2>
                    <p class="mb-0">Sistem Peminjaman Barang & Pengambilan ATK</p>
                    <small>Selamat datang di sistem manajemen peminjaman!</small>
                </div>
                <div class="col-md-4 text-right">
                    <div class="text-white-50">
                        <i class="fa fa-calendar"></i> <?php echo date('d F Y'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Peminjaman Hari Ini
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $peminjaman_hari_ini; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-handshake-o fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Belum Dikembalikan
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $belum_kembali; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-clock-o fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Pengambilan ATK Hari Ini
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $atk_hari_ini; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-shopping-cart fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Total Siswa
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_siswa; ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert for overdue items -->
        <?php if($terlambat > 0): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <strong>Perhatian!</strong> Ada <?php echo $terlambat; ?> peminjaman yang terlambat dikembalikan.
                    <a href="<?php print_link('peminjaman?search=terlambat'); ?>" class="alert-link">Lihat detail</a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Peminjaman Terbaru</h6>
                        <a href="<?php print_link('peminjaman'); ?>" class="btn btn-sm btn-primary">Lihat Semua</a>
                    </div>
                    <div class="card-body">
                        <?php if(!empty($recent_peminjaman)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Tanggal</th>
                                            <th>Siswa</th>
                                            <th>Barang</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($recent_peminjaman as $item): ?>
                                        <tr>
                                            <td><?php echo date('d/m', strtotime($item['tanggal_pinjam'])); ?></td>
                                            <td>
                                                <small>
                                                    <?php echo htmlspecialchars($item['nama_siswa'] ?? 'N/A'); ?><br>
                                                    <span class="text-muted"><?php echo htmlspecialchars($item['nama_kelas'] ?? ''); ?></span>
                                                </small>
                                            </td>
                                            <td><small><?php echo htmlspecialchars($item['barang']); ?></small></td>
                                            <td>
                                                <?php if($item['tanggal_kembali'] == '0000-00-00' || empty($item['tanggal_kembali'])): ?>
                                                    <span class="badge badge-warning">Dipinjam</span>
                                                <?php else: ?>
                                                    <span class="badge badge-success">Dikembalikan</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">Belum ada peminjaman</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-info">Pengambilan ATK Terbaru</h6>
                        <a href="<?php print_link('pengambilan_atk'); ?>" class="btn btn-sm btn-info">Lihat Semua</a>
                    </div>
                    <div class="card-body">
                        <?php if(!empty($recent_atk)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Tanggal</th>
                                            <th>Siswa</th>
                                            <th>Barang</th>
                                            <th>Jumlah</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($recent_atk as $item): ?>
                                        <tr>
                                            <td><?php echo date('d/m', strtotime($item['tanggal'])); ?></td>
                                            <td>
                                                <small>
                                                    <?php echo htmlspecialchars($item['nama_siswa'] ?? 'N/A'); ?><br>
                                                    <span class="text-muted"><?php echo htmlspecialchars($item['nama_kelas'] ?? ''); ?></span>
                                                </small>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo htmlspecialchars($item['nama_barang']); ?>
                                                    <?php if(!empty($item['nama_barang_2'])): ?>
                                                        <br><span class="text-muted">+ <?php echo htmlspecialchars($item['nama_barang_2']); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo $item['jumlah']; ?>
                                                    <?php if(!empty($item['jumlah_2'])): ?>
                                                        <br><span class="text-muted">+ <?php echo $item['jumlah_2']; ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">Belum ada pengambilan ATK</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Aksi Cepat</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3 mb-3">
                                <a href="<?php print_link('peminjaman/add'); ?>" class="btn btn-outline-primary btn-block">
                                    <i class="fa fa-plus-circle fa-2x mb-2"></i><br>
                                    Tambah Peminjaman
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="<?php print_link('pengambilan_atk/add'); ?>" class="btn btn-outline-info btn-block">
                                    <i class="fa fa-shopping-cart fa-2x mb-2"></i><br>
                                    Tambah Pengambilan ATK
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="<?php print_link('siswa/add'); ?>" class="btn btn-outline-success btn-block">
                                    <i class="fa fa-user-plus fa-2x mb-2"></i><br>
                                    Tambah Siswa
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="<?php print_link('kelas/add'); ?>" class="btn btn-outline-warning btn-block">
                                    <i class="fa fa-building fa-2x mb-2"></i><br>
                                    Tambah Kelas
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
