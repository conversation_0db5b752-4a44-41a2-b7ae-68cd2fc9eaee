<?php 
$page_id = null;
$comp_model = new SharedController;
$current_page = $this->set_current_page_link();
$data = $this->view_data;
$tahun_aktif = $data['tahun_aktif'];
$statistik = $data['statistik'];
$siswa_per_kelas = $data['siswa_per_kelas'];
?>

<div class="container-fluid">
    <!-- Header -->
    <div class="bg-success text-white p-4 mb-4 rounded">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1"><i class="fa fa-graduation-cap"></i> Dashboard Tahun Ajaran</h2>
                <p class="mb-0">Manajemen Tahun Ajaran & Naik <PERSON>las</p>
                <?php if($tahun_aktif): ?>
                    <small><strong>Tahun Aktif:</strong> <?php echo $tahun_aktif['tahun_ajaran']; ?> - Semester <?php echo $tahun_aktif['semester']; ?></small>
                <?php else: ?>
                    <small class="text-warning"><strong>⚠️ Belum ada tahun ajaran aktif!</strong></small>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-right">
                <div class="text-white-50">
                    <i class="fa fa-calendar"></i> <?php echo date('d F Y'); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert jika belum ada tahun ajaran aktif -->
    <?php if(!$tahun_aktif): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fa fa-exclamation-triangle"></i>
                <strong>Perhatian!</strong> Belum ada tahun ajaran aktif. 
                <a href="<?php print_link('tahun_ajaran/add'); ?>" class="alert-link">Tambah tahun ajaran baru</a> atau 
                <a href="<?php print_link('tahun_ajaran'); ?>" class="alert-link">aktifkan tahun ajaran yang ada</a>.
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fa fa-bolt"></i> Aksi Cepat Tahun Ajaran</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <a href="<?php print_link('tahun_ajaran/add'); ?>" class="btn btn-outline-success btn-block">
                                <i class="fa fa-plus-circle fa-2x mb-2"></i><br>
                                Tambah Tahun Ajaran
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php print_link('tahun_ajaran/naik_kelas'); ?>" class="btn btn-outline-warning btn-block" 
                               onclick="return confirm('Yakin ingin menjalankan proses naik kelas massal?')">
                                <i class="fa fa-level-up fa-2x mb-2"></i><br>
                                Naik Kelas Massal
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php print_link('siswa/add'); ?>" class="btn btn-outline-info btn-block">
                                <i class="fa fa-user-plus fa-2x mb-2"></i><br>
                                Tambah Siswa Baru
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php print_link('tahun_ajaran'); ?>" class="btn btn-outline-primary btn-block">
                                <i class="fa fa-list fa-2x mb-2"></i><br>
                                Kelola Tahun Ajaran
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistik Tahun Ajaran -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fa fa-bar-chart"></i> Statistik Per Tahun Ajaran</h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($statistik)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Tahun Ajaran</th>
                                        <th>Semester</th>
                                        <th>Total Siswa</th>
                                        <th>Total Peminjaman</th>
                                        <th>Total Pengambilan ATK</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($statistik as $stat): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $stat['tahun_ajaran']; ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $stat['semester'] == '1' ? 'primary' : 'secondary'; ?>">
                                                Semester <?php echo $stat['semester']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                <i class="fa fa-users"></i> <?php echo number_format($stat['total_siswa']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                <i class="fa fa-handshake-o"></i> <?php echo number_format($stat['total_peminjaman']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                <i class="fa fa-shopping-cart"></i> <?php echo number_format($stat['total_pengambilan_atk']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">Belum ada data statistik</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribusi Siswa Per Kelas (Tahun Aktif) -->
    <?php if($tahun_aktif && !empty($siswa_per_kelas)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fa fa-pie-chart"></i> 
                        Distribusi Siswa Per Kelas - <?php echo $tahun_aktif['tahun_ajaran']; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php 
                        $total_siswa_aktif = array_sum(array_column($siswa_per_kelas, 'jumlah_siswa'));
                        foreach($siswa_per_kelas as $kelas): 
                            $persentase = $total_siswa_aktif > 0 ? round(($kelas['jumlah_siswa'] / $total_siswa_aktif) * 100, 1) : 0;
                        ?>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                <?php echo $kelas['nama_kelas']; ?>
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php echo number_format($kelas['jumlah_siswa']); ?> siswa
                                            </div>
                                            <div class="text-xs text-muted">
                                                <?php echo $persentase; ?>% dari total
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fa fa-users fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <strong>Total Siswa Aktif: <?php echo number_format($total_siswa_aktif); ?> siswa</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Panduan Penggunaan -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fa fa-info-circle"></i> Panduan Tahun Ajaran Baru</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fa fa-check-circle text-success"></i> Langkah-langkah Tahun Ajaran Baru:</h6>
                            <ol>
                                <li>Tambah tahun ajaran baru dan set sebagai aktif</li>
                                <li>Jalankan proses naik kelas massal</li>
                                <li>Tambah siswa baru untuk kelas awal</li>
                                <li>Verifikasi data siswa per kelas</li>
                                <li>Mulai input peminjaman/ATK untuk tahun baru</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fa fa-lightbulb-o text-warning"></i> Tips Penting:</h6>
                            <ul>
                                <li>Data tahun lalu tetap tersimpan dan bisa diakses</li>
                                <li>Naik kelas otomatis berdasarkan mapping yang sudah diset</li>
                                <li>Siswa lulus akan otomatis berstatus "lulus"</li>
                                <li>Laporan bisa difilter per tahun ajaran</li>
                                <li>Backup database sebelum proses naik kelas</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
