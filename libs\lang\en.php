<?php

return array(
    'validate_required'                 => 'The {field} field is required',
    'validate_valid_email'              => 'The {field} field must be a valid email address',
    'validate_max_len'                  => 'The {field} field needs to be {param} characters or less',
    'validate_min_len'                  => 'The {field} field needs to be at least {param} characters',
    'validate_exact_len'                => 'The {field} field needs to be exactly {param} characters',
    'validate_alpha'                    => 'The {field} field may only contain letters',
    'validate_alpha_numeric'            => 'The {field} field may only contain letters and numbers',
    'validate_alpha_numeric_space'      => 'The {field} field may only contain letters, numbers and spaces',
    'validate_alpha_dash'               => 'The {field} field may only contain letters and dashes',
    'validate_alpha_space'              => 'The {field} field may only contain letters and spaces',
    'validate_numeric'                  => 'The {field} field must be a number',
    'validate_integer'                  => 'The {field} field must be a number without a decimal',
    'validate_boolean'                  => 'The {field} field has to be either true or false',
    'validate_float'                    => 'The {field} field must be a number with a decimal point (float)',
    'validate_valid_url'                => 'The {field} field has to be a URL',
    'validate_url_exists'               => 'The {field} URL does not exist',
    'validate_valid_ip'                 => 'The {field} field needs to be a valid IP address',
    'validate_valid_ipv4'               => 'The {field} field needs to contain a valid IPv4 address',
    'validate_valid_ipv6'               => 'The {field} field needs to contain a valid IPv6 address',
    'validate_guidv4'                   => 'The {field} field needs to contain a valid GUID',
    'validate_valid_cc'                 => 'The {field} is not a valid credit card number',
    'validate_valid_name'               => 'The {field} should be a full name',
    'validate_contains'                 => 'The {field} can only be one of the following: {param}',
    'validate_contains_list'            => 'The {field} is not a valid option',
    'validate_doesnt_contain_list'      => 'The {field} field contains a value that is not accepted',
    'validate_street_address'           => 'The {field} field needs to be a valid street address',
    'validate_date'                     => 'The {field} must be a valid date',
    'validate_min_numeric'              => 'The {field} field needs to be a numeric value, equal to, or higher than {param}',
    'validate_max_numeric'              => 'The {field} field needs to be a numeric value, equal to, or lower than {param}',
    'validate_min_age'                  => 'The {field} field needs to have an age greater than or equal to {param}',
    'validate_invalid'                  => 'The {field} field is invalid',
    'validate_starts'                   => 'The {field} field needs to start with {param}',
    'validate_extension'                => 'The {field} field can only have one of the following extensions: {param}',
    'validate_required_file'            => 'The {field} field is required',
    'validate_equalsfield'              => 'The {field} field does not equal {param} field',
    'validate_iban'                     => 'The {field} field needs to contain a valid IBAN',
    'validate_phone_number'             => 'The {field} field needs to be a valid Phone Number',
    'validate_regex'                    => 'The {field} field needs to contain a value with valid format',
    'validate_valid_json_string'        => 'The {field} field needs to contain a valid JSON format string',
    'validate_valid_array_size_greater' => 'The {field} fields needs to be an array with a size, equal to, or higher than {param}',
    'validate_valid_array_size_lesser'  => 'The {field} fields needs to be an array with a size, equal to, or lower than {param}',
    'validate_valid_array_size_equal'   => 'The {field} fields needs to be an array with a size equal to {param}',
    'validate_valid_persian_name'       => 'The {field} should be a valid Persian/Dari or Arabic name',
	'validate_valid_eng_per_pas_name'   => 'The {field} should be a valid English, Persian/Dari/Pashtu or Arabic name',
	'validate_valid_persian_digit'      => 'The {field} should be a valid digit in Persian/Dari or Arabic format',
	'validate_valid_persian_text'       => 'The {field} should be a valid text in Persian/Dari or Arabic format',
	'validate_valid_pashtu_text'        => 'The {field} should be a valid text in Pashtu format',
);
