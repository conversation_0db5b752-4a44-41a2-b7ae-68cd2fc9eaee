<?php

return array(
    'validate_required'                 => 'Das Feld "{field}" ist erforderlich.',
    'validate_valid_email'              => 'Das Feld "{field}" muss eine g&uuml;ltige E-Mail-Adresse sein.',
    'validate_max_len'                  => 'Das Feld "{field}" darf maximal {param} Zeichen enthalten.',
    'validate_min_len'                  => 'Das Feld "{field}" muss mindestens {param} Zeichen enthalten.',
    'validate_exact_len'                => 'Das Feld "{field}" muss genau {param} Zeichen enthalten.',
    'validate_alpha'                    => 'Das Feld "{field}" darf nur Buchstaben enthalten.',
    'validate_alpha_numeric'            => 'Das Feld "{field}" darf nur Buchstaben und Ziffern enthalten.',
    'validate_alpha_numeric_space'      => 'Das Feld "{field}" darf nur Buchstaben, Ziffern und Leerzeichen enthalten.',
    'validate_alpha_dash'               => 'Das Feld "{field}" darf nur Buchstaben und Bindestriche enthalten.',
    'validate_alpha_space'              => 'Das Feld "{field}" darf nur Buchstaben und Leerzeichen enthalten.',
    'validate_numeric'                  => 'Das Feld "{field}" muss eine Nummer sein.',
    'validate_integer'                  => 'Das Feld "{field}" muss eine Nummer ohne Nachkommastellen (ganze Zahl) sein.',
    'validate_boolean'                  => 'Das Feld "{field}" muss entweder wahr oder falsch sein.',
    'validate_float'                    => 'Das Feld "{field}" muss eine Nummer mit einem Dezimalpunkt (Gleitpunktzahl) sein.',
    'validate_valid_url'                => 'Das Feld "{field}" muss eine URL sein.',
    'validate_url_exists'               => 'Die im Feld "{field}" angegebene URL existiert nicht.',
    'validate_valid_ip'                 => 'Das Feld "{field}" muss eine g&uuml;ltige IP-Adresse sein.',
    'validate_valid_ipv4'               => 'Das Feld "{field}" muss eine g&uuml;ltige IPv4-Adresse enthalten.',
    'validate_valid_ipv6'               => 'Das Feld "{field}" muss eine g&uuml;ltige IPv6-Adresse enthalten.',
    'validate_guidv4'                   => 'Das Feld "{field}" muss eine g&uuml;ltige GUID enthalten.',
    'validate_valid_cc'                 => 'Das Feld "{field}" ist keine g&uuml;ltige Kreditkartennummer.',
    'validate_valid_name'               => 'Das Feld "{field}" muss ein voller Name sein.',
    'validate_contains'                 => 'Das Feld "{field}" kann nur eines der folgenden sein: {param}',
    'validate_contains_list'            => 'Das Feld "{field}" ist keine g&uuml;ltige Wahl.',
    'validate_doesnt_contain_list'      => 'Das Feld "{field}" enth&auml;lt einen nicht akzeptierten Wert.',
    'validate_street_address'           => 'Das Feld "{field}" muss eine g&uuml;ltige Stra&szlig;enangabe sein.',
    'validate_date'                     => 'Das Feld "{field}" muss ein g&uuml;ltiges Datum sein.',
    'validate_min_numeric'              => 'Das Feld "{field}" muss ein numerischer Wert gr&ouml;&szlig;ergleich {param} sein.',
    'validate_max_numeric'              => 'Das Feld "{field}" muss ein numerischer Wert kleinergleich {param} sein.',
    'validate_min_age'                  => 'Das Feld "{field}" muss ein Alter gr&ouml;&szlig;ergleich {param} haben.',
    'validate_invalid'                  => 'Das Feld "{field}" ist ung&uuml;ltig.',
    'validate_starts'                   => 'Das Feld "{field}" muss mit {param} beginnen.',
    'validate_extension'                => 'Das Feld "{field}" kann nur eine der folgenden Erweiterungen haben: {param}',
    'validate_required_file'            => 'Das Feld "{field}" ist erforderlich.',
    'validate_equalsfield'              => 'Das Feld "{field}" ist nicht gleich dem Feld "{param}".',
    'validate_iban'                     => 'Das Feld "{field}" muss eine g&uuml;ltige IBAN enthalten.',
    'validate_phone_number'             => 'Das Feld "{field}" muss eine g&uuml;ltige Telefonnummer sein.',
    'validate_regex'                    => 'Das Feld "{field}" muss einen Wert im g&uuml;ltigem Format enthalten.',
    'validate_valid_json_string'        => 'Das Feld "{field}" muss eine g&uuml;ltige JSON-Format-Zeichenfolge enthalten.',
    'validate_valid_array_size_greater' => 'Das Feld "{field}" muss ein Array mit einer Gr&ouml;&szlig;e gr&ouml;&szlig;ergleich {param} sein.',
    'validate_valid_array_size_lesser'  => 'Das Feld "{field}" muss ein Array mit einer Gr&ouml;&szlig;e kleinergleich {param} sein.',
    'validate_valid_array_size_equal'   => 'Das Feld "{field}" muss ein Array mit einer Gr&ouml;&szlig;e gleich {param} sein.',
);
