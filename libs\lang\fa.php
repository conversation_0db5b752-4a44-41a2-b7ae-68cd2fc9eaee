<?php

return array(
    'validate_required'                 => '{field} الزامی میباشد',
    'validate_valid_email'              => '{field} باید یک آدرس ایمیل معتبر باشد ',
    'validate_max_len'                  => '{field} باید حد اکثر {param} کاراکتر یا کمتر باشد',
    'validate_min_len'                  => '{field} باید حد اقل {param} کاراکتر یا بیشتر باشد',
    'validate_exact_len'                => '{field} باید دقیقا {param} کاراکتر باشد',
    'validate_alpha'                    => '{field} فقط باید شامل حروف باشد',
    'validate_alpha_numeric'            => '{field} فقط باید شامل حروف و اعداد باشد',
    'validate_alpha_numeric_space'      => '{field} فقط باید شامل حروف، اعداد و فاصله باشد',
    'validate_alpha_dash'               => '{field} فقط باید شامل حروف و خط فاصله یا خط تیره باشد',
    'validate_alpha_space'              => '{field} فقط باید شامل حروف و فاصله باشد',
    'validate_numeric'                  => '{field} باید یک عدد باشد',
    'validate_integer'                  => '{field} باید یک عدد بدون نقطه اعشار یا ممیز باشد',
    'validate_boolean'                  => '{field} باید درست/صحیح یا نادرست/غلط باشد',
    'validate_float'                    => '{field} باید عدد با نقطه اعشار یا ممیز باشد',
    'validate_valid_url'                => '{field} باید یک آدرس وب باشد',
    'validate_url_exists'               => '{field} آدرس وب/اینترنت وجود ندارد',
    'validate_valid_ip'                 => '{field} باید یک IP آدرس معتبر باشد',
    'validate_valid_ipv4'               => '{field} باید یک آدرس IPv4 معتبر باشد',
    'validate_valid_ipv6'               => '{field} باید یک آدرس IPv6 معتبر باشد',
    'validate_guidv4'                   => '{field} باید یک GUID معتبر داشته باشد',
    'validate_valid_cc'                 => '{field} شماره کارت اعتباری درست نیست',
    'validate_valid_name'               => '{field} باید یک نام معتبر باشد',
    'validate_contains'                 => 'The {field} فقط میتواند یکی از موارد {param} زیر باشد',
    'validate_contains_list'            => '{field} این گزینه درست نیست',
    'validate_doesnt_contain_list'      => '{field} شامل یک مقدار نادرست هست',
    'validate_street_address'           => '{field} باید یک آدرس خیابان معتبر باشد',
    'validate_date'                     => '{field} باید یک تاریخ معتبر باشد',
    'validate_min_numeric'              => '{field} باید یک مقدار عددی مساوی یا بزرگتر از {param} باشد',
    'validate_max_numeric'              => '{field} باید یک مقدار عددی مساوی یا کوچکتر از {param} باشد',
    'validate_min_age'                  => '{field} باید مقدار عمر یا سن شخص مساوی یا بزرگتر از {param} باشد',
    'validate_invalid'                  => '{field} درست یا معتبر نیست',
    'validate_starts'                   => '{field} باید با {param} شروع گردد',
    'validate_extension'                => '{field} فقط میتواند یکی از پسوند های {param} را داشته باشد',
    'validate_required_file'            => '{field} الزامی است',
    'validate_equalsfield'              => '{field} برابر با {param} نمی باشد',
    'validate_iban'                     => '{field} باید یک IBAN معتبر باشد',
    'validate_phone_number'             => '{field} باید یک شماره تلفن معتبر باشد',
    'validate_regex'                    => '{field} باید شامل یک مقدار با فرمت درست و معتبر باشد',
    'validate_valid_json_string'        => '{field} باید شامل یک فرمت درست و معتبر JSON باشد',
    'validate_valid_array_size_greater' => '{field} باید یک آرایه که اندازه آن مساوی یا بزرگتر از {param} باشد',
    'validate_valid_array_size_lesser'  => '{field} باید یک آرایه که اندازه آن مساوی یا کوچکتر از {param} باشد',
    'validate_valid_array_size_equal'   => '{field} باید یک آرایه که اندازه آن برابر با {param} باشد',
    'validate_valid_persian_name'       => '{field} باید یک نام معتبر فارسی/دری یا عربی باشد',
	'validate_valid_eng_per_pas_name'   => '{field} باید یک نام معتبر انگلیسی، فارسی/دری، پشتو یا عربی باشد',
	'validate_valid_persian_digit'      => '{field} باید یک عدد معتبر با فرمت فارسی/دری/پشتو یا عربی باشد',
	'validate_valid_persian_text'       => '{field} باید یک متن معتبر به زبان فارسی/دری باشد',
	'validate_valid_pashtu_text'        => '{field} باید یک متن معتبر به زبان پشتو باشد',
);
