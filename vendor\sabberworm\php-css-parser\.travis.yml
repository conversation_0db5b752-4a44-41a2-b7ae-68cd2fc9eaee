language: php
php:
    - "5.4"
    - "5.5"
    - "5.6"
    - "7.0"
    - "7.1"
    - "7.2"
    - "7.3"
    - hhvm
sudo: false
dist: trusty
matrix:
    include:
        -
            php: "5.3"
            dist: precise
            sudo: false
before_script: rm composer.lock && composer install
script: ./vendor/bin/phpunit --coverage-clover build/coverage/xml
after_script: ./vendor/bin/codacycoverage clover build/coverage/xml

