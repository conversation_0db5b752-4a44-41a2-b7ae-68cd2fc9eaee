<?php 
/**
 * Pengambilan_atk Page Controller
 * @category  Controller
 */
class Pengambilan_atkController extends BaseController{
	function __construct(){
		parent::__construct();
		$this->tablename = "pengambilan_atk";
	}
	/**
     * List page records
     * @param $fieldname (filter record by a field) 
     * @param $fieldvalue (filter field value)
     * @return BaseView
     */
	function index($fieldname = null , $fieldvalue = null){
		$request = $this->request;
		$db = $this->GetModel();
		$tablename = $this->tablename;
		$fields = array("pengambilan_atk.id_pengambilan_atk", 
			"pengambilan_atk.tanggal", 
			"pengambilan_atk.nama", 
			"siswa.nama_siswa AS siswa_nama_siswa", 
			"pengambilan_atk.kelas", 
			"kelas.nama_kelas AS kelas_nama_kelas", 
			"pengambilan_atk.nama_barang", 
			"pengambilan_atk.jumlah", 
			"pengambilan_atk.nama_barang_2", 
			"pengambilan_atk.jumlah_2");
		$pagination = $this->get_pagination(MAX_RECORD_COUNT); // get current pagination e.g array(page_number, page_limit)
		//search table record
		if(!empty($request->search)){
			$text = trim($request->search); 
			$search_condition = "(
				pengambilan_atk.id_pengambilan_atk LIKE ? OR 
				pengambilan_atk.tanggal LIKE ? OR 
				pengambilan_atk.nama LIKE ? OR 
				pengambilan_atk.kelas LIKE ? OR 
				pengambilan_atk.nama_barang LIKE ? OR 
				pengambilan_atk.jumlah LIKE ? OR 
				pengambilan_atk.nama_barang_2 LIKE ? OR 
				pengambilan_atk.jumlah_2 LIKE ?
			)";
			$search_params = array(
				"%$text%","%$text%","%$text%","%$text%","%$text%","%$text%","%$text%","%$text%"
			);
			//setting search conditions
			$db->where($search_condition, $search_params);
			 //template to use when ajax search
			$this->view->search_template = "pengambilan_atk/search.php";
		}
		$db->join("siswa", "pengambilan_atk.nama = siswa.id_siswa", "INNER");
		$db->join("kelas", "pengambilan_atk.kelas = kelas.id_kelas", "INNER");
		if(!empty($request->orderby)){
			$orderby = $request->orderby;
			$ordertype = (!empty($request->ordertype) ? $request->ordertype : ORDER_TYPE);
			$db->orderBy($orderby, $ordertype);
		}
		else{
			$db->orderBy("pengambilan_atk.id_pengambilan_atk", ORDER_TYPE);
		}
		if($fieldname){
			$db->where($fieldname , $fieldvalue); //filter by a single field name
		}
		$tc = $db->withTotalCount();
		$records = $db->get($tablename, $pagination, $fields);
		$records_count = count($records);
		$total_records = intval($tc->totalCount);
		$page_limit = $pagination[1];
		$total_pages = ceil($total_records / $page_limit);
		if(	!empty($records)){
			foreach($records as &$record){
				$record['tanggal'] = format_date($record['tanggal'],'d-m-Y');
			}
		}
		$data = new stdClass;
		$data->records = $records;
		$data->record_count = $records_count;
		$data->total_records = $total_records;
		$data->total_page = $total_pages;
		if($db->getLastError()){
			$this->set_page_error();
		}
		$page_title = $this->view->page_title = "Pengambilan Atk";
		$this->view->report_filename = date('Y-m-d') . '-' . $page_title;
		$this->view->report_title = $page_title;
		$this->view->report_layout = "report_layout.php";
		$this->view->report_paper_size = "A4";
		$this->view->report_orientation = "portrait";
		$this->render_view("pengambilan_atk/list.php", $data); //render the full page
	}
	/**
     * View record detail 
	 * @param $rec_id (select record by table primary key) 
     * @param $value value (select record by value of field name(rec_id))
     * @return BaseView
     */
	function view($rec_id = null, $value = null){
		$request = $this->request;
		$db = $this->GetModel();
		$rec_id = $this->rec_id = urldecode($rec_id);
		$tablename = $this->tablename;
		$fields = array("pengambilan_atk.id_pengambilan_atk", 
			"pengambilan_atk.nama", 
			"siswa.nama_siswa AS siswa_nama_siswa", 
			"pengambilan_atk.kelas", 
			"kelas.nama_kelas AS kelas_nama_kelas", 
			"pengambilan_atk.nama_barang", 
			"pengambilan_atk.tanggal", 
			"pengambilan_atk.jumlah", 
			"pengambilan_atk.nama_barang_2", 
			"pengambilan_atk.jumlah_2");
		if($value){
			$db->where($rec_id, urldecode($value)); //select record based on field name
		}
		else{
			$db->where("pengambilan_atk.id_pengambilan_atk", $rec_id);; //select record based on primary key
		}
		$db->join("siswa", "pengambilan_atk.nama = siswa.id_siswa", "INNER");
		$db->join("kelas", "pengambilan_atk.kelas = kelas.id_kelas", "INNER");  
		$record = $db->getOne($tablename, $fields );
		if($record){
			$record['tanggal'] = format_date($record['tanggal'],'d-m-Y');
			$page_title = $this->view->page_title = "View  Pengambilan Atk";
		$this->view->report_filename = date('Y-m-d') . '-' . $page_title;
		$this->view->report_title = $page_title;
		$this->view->report_layout = "report_layout.php";
		$this->view->report_paper_size = "A4";
		$this->view->report_orientation = "portrait";
		}
		else{
			if($db->getLastError()){
				$this->set_page_error();
			}
			else{
				$this->set_page_error("No record found");
			}
		}
		return $this->render_view("pengambilan_atk/view.php", $record);
	}
	/**
     * Insert new record to the database table
	 * @param $formdata array() from $_POST
     * @return BaseView
     */
	function add($formdata = null){
		if($formdata){
			$db = $this->GetModel();
			$tablename = $this->tablename;
			$request = $this->request;
			//fillable fields
			$fields = $this->fields = array("tanggal","kelas","nama","nama_barang","jumlah","nama_barang_2","jumlah_2");
			$postdata = $this->format_request_data($formdata);
			$this->rules_array = array(
				'tanggal' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'nama_barang' => 'required',
				'jumlah' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'nama_barang' => 'sanitize_string',
				'jumlah' => 'sanitize_string',
				'nama_barang_2' => 'sanitize_string',
				'jumlah_2' => 'sanitize_string',
			);
			$this->filter_vals = true; //set whether to remove empty fields
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$rec_id = $this->rec_id = $db->insert($tablename, $modeldata);
				if($rec_id){
					$this->set_flash_msg("Record added successfully", "success");
					return	$this->redirect("pengambilan_atk");
				}
				else{
					$this->set_page_error();
				}
			}
		}
		$page_title = $this->view->page_title = "Add New Pengambilan Atk";
		$this->render_view("pengambilan_atk/add.php");
	}
	/**
     * Update table record with formdata
	 * @param $rec_id (select record by table primary key)
	 * @param $formdata array() from $_POST
     * @return array
     */
	function edit($rec_id = null, $formdata = null){
		$request = $this->request;
		$db = $this->GetModel();
		$this->rec_id = $rec_id;
		$tablename = $this->tablename;
		 //editable fields
		$fields = $this->fields = array("id_pengambilan_atk","tanggal","kelas","nama","nama_barang","jumlah","nama_barang_2","jumlah_2");
		if($formdata){
			$postdata = $this->format_request_data($formdata);
			$this->rules_array = array(
				'tanggal' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'nama_barang' => 'required',
				'jumlah' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'nama_barang' => 'sanitize_string',
				'jumlah' => 'sanitize_string',
				'nama_barang_2' => 'sanitize_string',
				'jumlah_2' => 'sanitize_string',
			);
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$db->where("pengambilan_atk.id_pengambilan_atk", $rec_id);;
				$bool = $db->update($tablename, $modeldata);
				$numRows = $db->getRowCount(); //number of affected rows. 0 = no record field updated
				if($bool && $numRows){
					$this->set_flash_msg("Record updated successfully", "success");
					return $this->redirect("pengambilan_atk");
				}
				else{
					if($db->getLastError()){
						$this->set_page_error();
					}
					elseif(!$numRows){
						//not an error, but no record was updated
						$page_error = "No record updated";
						$this->set_page_error($page_error);
						$this->set_flash_msg($page_error, "warning");
						return	$this->redirect("pengambilan_atk");
					}
				}
			}
		}
		$db->where("pengambilan_atk.id_pengambilan_atk", $rec_id);;
		$data = $db->getOne($tablename, $fields);
		$page_title = $this->view->page_title = "Edit  Pengambilan Atk";
		if(!$data){
			$this->set_page_error();
		}
		return $this->render_view("pengambilan_atk/edit.php", $data);
	}
	/**
     * Update single field
	 * @param $rec_id (select record by table primary key)
	 * @param $formdata array() from $_POST
     * @return array
     */
	function editfield($rec_id = null, $formdata = null){
		$db = $this->GetModel();
		$this->rec_id = $rec_id;
		$tablename = $this->tablename;
		//editable fields
		$fields = $this->fields = array("id_pengambilan_atk","tanggal","kelas","nama","nama_barang","jumlah","nama_barang_2","jumlah_2");
		$page_error = null;
		if($formdata){
			$postdata = array();
			$fieldname = $formdata['name'];
			$fieldvalue = $formdata['value'];
			$postdata[$fieldname] = $fieldvalue;
			$postdata = $this->format_request_data($postdata);
			$this->rules_array = array(
				'tanggal' => 'required',
				'kelas' => 'required',
				'nama' => 'required',
				'nama_barang' => 'required',
				'jumlah' => 'required',
			);
			$this->sanitize_array = array(
				'tanggal' => 'sanitize_string',
				'kelas' => 'sanitize_string',
				'nama' => 'sanitize_string',
				'nama_barang' => 'sanitize_string',
				'jumlah' => 'sanitize_string',
				'nama_barang_2' => 'sanitize_string',
				'jumlah_2' => 'sanitize_string',
			);
			$this->filter_rules = true; //filter validation rules by excluding fields not in the formdata
			$modeldata = $this->modeldata = $this->validate_form($postdata);
			if($this->validated()){
				$db->where("pengambilan_atk.id_pengambilan_atk", $rec_id);;
				$bool = $db->update($tablename, $modeldata);
				$numRows = $db->getRowCount();
				if($bool && $numRows){
					return render_json(
						array(
							'num_rows' =>$numRows,
							'rec_id' =>$rec_id,
						)
					);
				}
				else{
					if($db->getLastError()){
						$page_error = $db->getLastError();
					}
					elseif(!$numRows){
						$page_error = "No record updated";
					}
					render_error($page_error);
				}
			}
			else{
				render_error($this->view->page_error);
			}
		}
		return null;
	}
	/**
     * Delete record from the database
	 * Support multi delete by separating record id by comma.
     * @return BaseView
     */
	function delete($rec_id = null){
		Csrf::cross_check();
		$request = $this->request;
		$db = $this->GetModel();
		$tablename = $this->tablename;
		$this->rec_id = $rec_id;
		//form multiple delete, split record id separated by comma into array
		$arr_rec_id = array_map('trim', explode(",", $rec_id));
		$db->where("pengambilan_atk.id_pengambilan_atk", $arr_rec_id, "in");
		$bool = $db->delete($tablename);
		if($bool){
			$this->set_flash_msg("Record deleted successfully", "success");
		}
		elseif($db->getLastError()){
			$page_error = $db->getLastError();
			$this->set_flash_msg($page_error, "danger");
		}
		return	$this->redirect("pengambilan_atk");
	}
}
