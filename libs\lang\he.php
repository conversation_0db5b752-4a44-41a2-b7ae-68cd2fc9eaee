<?php

return array(
    'validate_required'                 => 'שדה {field} הינו חובה',
    'validate_valid_email'              => 'שדה {field} מוכרח להיות כתובת דוא"ל חוקית',
    'validate_max_len'                  => 'שדה {field} מוכרח להיות {param} תווים או פחות',
    'validate_min_len'                  => 'שדה {field} מוכרח להיות לפחות {param} תווים',
    'validate_exact_len'                => 'שדה {field} מוכרח להיות בדיוק {param} תווים',
    'validate_alpha'                    => 'שדה {field} יכול להכיל אותיות בלבד',
    'validate_alpha_numeric'            => 'שדה {field} יכול להכיל אותיות ומספרים בלבד',
    'validate_alpha_numeric_space'      => 'שדה {field} יכול להכיל אותיות, מספרים ורווחים בלבד',
    'validate_alpha_dash'               => 'שדה {field} יכול להכיל אותיות ומקפים בלבד',
    'validate_alpha_space'              => 'שדה {field} יכול להכיל אותיות ורווחים בלבד',
    'validate_numeric'                  => 'שדה {field} מוכרח להיות מספר',
    'validate_integer'                  => 'שדה {field} מוכרח להיות מספר שלם',
    'validate_boolean'                  => 'שדה {field} מוכרח להיות אמת או שקר',
    'validate_float'                    => 'שדה {field} מוכרח להיות מספר עם נקודה עשרונית',
    'validate_valid_url'                => 'שדה {field} מוכרח להיות כתובת אתר',
    'validate_url_exists'               => 'שדה {field} אינו כתובת אתר קיימת',
    'validate_valid_ip'                 => 'שדה {field} מוכרח להיות כתובת IP חוקית',
    'validate_valid_ipv4'               => 'שדה {field} מוכרח להכיל כתובת IPv4 חוקית',
    'validate_valid_ipv6'               => 'שדה {field} מוכרח להכיל כתובת IPv6 חוקית',
    'validate_guidv4'                   => 'שדה {field} מוכרח להכיל GUID תקין',
    'validate_valid_cc'                 => 'שדה {field} אינו מספר כרטיס אשראי חוקי',
    'validate_valid_name'               => 'שדה {field} מוכרח להכיל שם מלא',
    'validate_contains'                 => 'שדה {field} יכול להכיל רק אחד מן הערכים הבאים: {param}',
    'validate_contains_list'            => 'שדה {field} אינו אפשרות חוקית',
    'validate_doesnt_contain_list'      => 'שדה {field} מכיל ערך שאינו מקובל',
    'validate_street_address'           => 'שדה {field} מוכרח להיות כתובת רחוב חוקית',
    'validate_date'                     => 'שדה {field} מוכרח להיות תאריך חוקי',
    'validate_min_numeric'              => 'שדה {field} מוכרח להיות ערך מספרי, שווה ל, או גבוה מ {param}',
    'validate_max_numeric'              => 'שדה {field} מוכרח להיות ערך מספרי, שווה או נמוך מ {param}',
    'validate_min_age'                  => 'שדה {field} מוכרח להיות גיל גדול או שווה ל {param}',
    'validate_invalid'                  => 'שדה {field} אינו חוקי',
    'validate_starts'                   => 'שדה {field} מוכרח להתחיל עם {param}',
    'validate_extension'                => 'שדה {field} יכול להיות רק אחת מן הסיומות הבאות: {param}',
    'validate_required_file'            => 'שדה {field} הינו שדה קובץ חובה',
    'validate_equalsfield'              => 'שדה {field} אינו שווה לשדה {param}',
    'validate_iban'                     => 'שדה {field} מוכרח להכיל IBAN חוקי',
    'validate_phone_number'             => 'שדה {field} מוכרח להיות מספר טלפון חוקי',
    'validate_regex'                    => 'שדה {field} מוכרח להכיל ערך בפורמט חוקי',
    'validate_valid_json_string'        => 'שדה {field} להכיל מחרוזת בפורמט JSON חוקי',
    'validate_valid_array_size_greater' => 'שדה {field} מוכרח להיות מערך בעל גודל, שווה ל, או גבוה מ {param}',
    'validate_valid_array_size_lesser'  => 'שדה {field} מוכרח להיות מערך עם גודל, שווה או נמוך מ {param}',
    'validate_valid_array_size_equal'   => 'שדה {field} מוכרח להיות מערך עם גודל שווה ל {param}',
);
