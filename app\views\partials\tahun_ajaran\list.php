<?php 
$page_id = null;
$comp_model = new SharedController;
$current_page = $this->set_current_page_link();
$data = $this->view_data;
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fa fa-graduation-cap"></i> Manajemen Tahun Ajaran</h4>
                    <div>
                        <a href="<?php print_link('tahun_ajaran/add'); ?>" class="btn btn-success btn-sm">
                            <i class="fa fa-plus"></i> Tambah Tahun Ajaran
                        </a>
                        <a href="<?php print_link('tahun_ajaran/dashboard'); ?>" class="btn btn-info btn-sm">
                            <i class="fa fa-dashboard"></i> Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search Form -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="get" action="<?php print_link('tahun_ajaran'); ?>">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Cari tahun ajaran..." 
                                           value="<?php echo get_value('search'); ?>">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6 text-right">
                            <small class="text-muted">
                                Total: <?php echo $data->total_records; ?> tahun ajaran
                            </small>
                        </div>
                    </div>

                    <!-- Table -->
                    <?php if(!empty($data->records)): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>Tahun Ajaran</th>
                                    <th>Semester</th>
                                    <th>Periode</th>
                                    <th>Status</th>
                                    <th>Keterangan</th>
                                    <th width="150">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($data->records as $record): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $record['tahun_ajaran']; ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo $record['semester'] == '1' ? 'primary' : 'secondary'; ?>">
                                            Semester <?php echo $record['semester']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo $record['tanggal_mulai']; ?> s/d <?php echo $record['tanggal_selesai']; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if($record['is_active']): ?>
                                            <span class="badge badge-success">
                                                <i class="fa fa-check-circle"></i> Aktif
                                            </span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">
                                                <i class="fa fa-circle-o"></i> Tidak Aktif
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo $record['keterangan'] ?: '-'; ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <?php if(!$record['is_active']): ?>
                                            <a href="<?php print_link('tahun_ajaran/set_active/' . $record['id_tahun_ajaran']); ?>" 
                                               class="btn btn-success btn-sm" 
                                               onclick="return confirm('Aktifkan tahun ajaran ini?')"
                                               title="Aktifkan">
                                                <i class="fa fa-check"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <a href="<?php print_link('tahun_ajaran/view/' . $record['id_tahun_ajaran']); ?>" 
                                               class="btn btn-info btn-sm" title="Lihat Detail">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            
                                            <a href="<?php print_link('tahun_ajaran/edit/' . $record['id_tahun_ajaran']); ?>" 
                                               class="btn btn-warning btn-sm" title="Edit">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            
                                            <?php if(!$record['is_active']): ?>
                                            <a href="<?php print_link('tahun_ajaran/delete/' . $record['id_tahun_ajaran']); ?>" 
                                               class="btn btn-danger btn-sm" 
                                               onclick="return confirm('Yakin ingin menghapus tahun ajaran ini?')"
                                               title="Hapus">
                                                <i class="fa fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($data->total_page > 1): ?>
                    <div class="row">
                        <div class="col-12">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php 
                                    $current_page = get_numeric_value(get_value('page'), 1);
                                    $prev_page = $current_page - 1;
                                    $next_page = $current_page + 1;
                                    ?>
                                    
                                    <?php if($current_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php print_link('tahun_ajaran?page=' . $prev_page); ?>">
                                            <i class="fa fa-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for($i = 1; $i <= $data->total_page; $i++): ?>
                                    <li class="page-item <?php echo ($i == $current_page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="<?php print_link('tahun_ajaran?page=' . $i); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if($current_page < $data->total_page): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="<?php print_link('tahun_ajaran?page=' . $next_page); ?>">
                                            Next <i class="fa fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fa fa-graduation-cap fa-5x text-muted mb-3"></i>
                        <h5 class="text-muted">Belum ada tahun ajaran</h5>
                        <p class="text-muted">Mulai dengan menambahkan tahun ajaran pertama</p>
                        <a href="<?php print_link('tahun_ajaran/add'); ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Tambah Tahun Ajaran
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
