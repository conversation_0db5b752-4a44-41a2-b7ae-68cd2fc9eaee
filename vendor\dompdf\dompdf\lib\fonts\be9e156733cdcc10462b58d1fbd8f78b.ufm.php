<?php return array (
  'codeToName' => 
  array (
    32 => 'space',
    168 => 'dieresis',
    169 => 'copyright',
    174 => 'registered',
    180 => 'acute',
    198 => 'AE',
    216 => 'Oslash',
    8482 => 'trademark',
    8734 => 'infinity',
    8800 => 'notequal',
    61440 => 'glass',
    61441 => 'music',
    61442 => 'search',
    61443 => 'envelope',
    61444 => 'heart',
    61445 => 'star',
    61446 => 'star_empty',
    61447 => 'user',
    61448 => 'film',
    61449 => 'th_large',
    61450 => 'th',
    61451 => 'th_list',
    61452 => 'ok',
    61453 => 'remove',
    61454 => 'zoom_in',
    61456 => 'zoom_out',
    61457 => 'off',
    61458 => 'signal',
    61459 => 'cog',
    61460 => 'trash',
    61461 => 'home',
    61462 => 'file_alt',
    61463 => 'time',
    61464 => 'road',
    61465 => 'download_alt',
    61466 => 'download',
    61467 => 'upload',
    61468 => 'inbox',
    61469 => 'play_circle',
    61470 => 'repeat',
    61473 => 'refresh',
    61474 => 'list_alt',
    61475 => 'lock',
    61476 => 'flag',
    61477 => 'headphones',
    61478 => 'volume_off',
    61479 => 'volume_down',
    61480 => 'volume_up',
    61481 => 'qrcode',
    61482 => 'barcode',
    61483 => 'tag',
    61484 => 'tags',
    61485 => 'book',
    61486 => 'bookmark',
    61487 => 'print',
    61488 => 'camera',
    61489 => 'font',
    61490 => 'bold',
    61491 => 'italic',
    61492 => 'text_height',
    61493 => 'text_width',
    61494 => 'align_left',
    61495 => 'align_center',
    61496 => 'align_right',
    61497 => 'align_justify',
    61498 => 'list',
    61499 => 'indent_left',
    61500 => 'indent_right',
    61501 => 'facetime_video',
    61502 => 'picture',
    61504 => 'pencil',
    61505 => 'map_marker',
    61506 => 'adjust',
    61507 => 'tint',
    61508 => 'edit',
    61509 => 'share',
    61510 => 'check',
    61511 => 'move',
    61512 => 'step_backward',
    61513 => 'fast_backward',
    61514 => 'backward',
    61515 => 'play',
    61516 => 'pause',
    61517 => 'stop',
    61518 => 'forward',
    61520 => 'fast_forward',
    61521 => 'step_forward',
    61522 => 'eject',
    61523 => 'chevron_left',
    61524 => 'chevron_right',
    61525 => 'plus_sign',
    61526 => 'minus_sign',
    61527 => 'remove_sign',
    61528 => 'ok_sign',
    61529 => 'question_sign',
    61530 => 'info_sign',
    61531 => 'screenshot',
    61532 => 'remove_circle',
    61533 => 'ok_circle',
    61534 => 'ban_circle',
    61536 => 'arrow_left',
    61537 => 'arrow_right',
    61538 => 'arrow_up',
    61539 => 'arrow_down',
    61540 => 'share_alt',
    61541 => 'resize_full',
    61542 => 'resize_small',
    61543 => 'plus',
    61544 => 'minus',
    61545 => 'asterisk',
    61546 => 'exclamation_sign',
    61547 => 'gift',
    61548 => 'leaf',
    61549 => 'fire',
    61550 => 'eye_open',
    61552 => 'eye_close',
    61553 => 'warning_sign',
    61554 => 'plane',
    61555 => 'calendar',
    61556 => 'random',
    61557 => 'comment',
    61558 => 'magnet',
    61559 => 'chevron_up',
    61560 => 'chevron_down',
    61561 => 'retweet',
    61562 => 'shopping_cart',
    61563 => 'folder_close',
    61564 => 'folder_open',
    61565 => 'resize_vertical',
    61566 => 'resize_horizontal',
    61568 => 'bar_chart',
    61569 => 'twitter_sign',
    61570 => 'facebook_sign',
    61571 => 'camera_retro',
    61572 => 'key',
    61573 => 'cogs',
    61574 => 'comments',
    61575 => 'thumbs_up_alt',
    61576 => 'thumbs_down_alt',
    61577 => 'star_half',
    61578 => 'heart_empty',
    61579 => 'signout',
    61580 => 'linkedin_sign',
    61581 => 'pushpin',
    61582 => 'external_link',
    61584 => 'signin',
    61585 => 'trophy',
    61586 => 'github_sign',
    61587 => 'upload_alt',
    61588 => 'lemon',
    61589 => 'phone',
    61590 => 'check_empty',
    61591 => 'bookmark_empty',
    61592 => 'phone_sign',
    61593 => 'twitter',
    61594 => 'facebook',
    61595 => 'github',
    61596 => 'unlock',
    61597 => 'credit_card',
    61598 => 'rss',
    61600 => 'hdd',
    61601 => 'bullhorn',
    61602 => 'bell',
    61603 => 'certificate',
    61604 => 'hand_right',
    61605 => 'hand_left',
    61606 => 'hand_up',
    61607 => 'hand_down',
    61608 => 'circle_arrow_left',
    61609 => 'circle_arrow_right',
    61610 => 'circle_arrow_up',
    61611 => 'circle_arrow_down',
    61612 => 'globe',
    61613 => 'wrench',
    61614 => 'tasks',
    61616 => 'filter',
    61617 => 'briefcase',
    61618 => 'fullscreen',
    61632 => 'group',
    61633 => 'link',
    61634 => 'cloud',
    61635 => 'beaker',
    61636 => 'cut',
    61637 => 'copy',
    61638 => 'paper_clip',
    61639 => 'save',
    61640 => 'sign_blank',
    61641 => 'reorder',
    61642 => 'ul',
    61643 => 'ol',
    61644 => 'strikethrough',
    61645 => 'underline',
    61646 => 'table',
    61648 => 'magic',
    61649 => 'truck',
    61650 => 'pinterest',
    61651 => 'pinterest_sign',
    61652 => 'google_plus_sign',
    61653 => 'google_plus',
    61654 => 'money',
    61655 => 'caret_down',
    61656 => 'caret_up',
    61657 => 'caret_left',
    61658 => 'caret_right',
    61659 => 'columns',
    61660 => 'sort',
    61661 => 'sort_down',
    61662 => 'sort_up',
    61664 => 'envelope_alt',
    61665 => 'linkedin',
    61666 => 'undo',
    61667 => 'legal',
    61668 => 'dashboard',
    61669 => 'comment_alt',
    61670 => 'comments_alt',
    61671 => 'bolt',
    61672 => 'sitemap',
    61673 => 'umbrella',
    61674 => 'paste',
    61675 => 'light_bulb',
    61676 => 'exchange',
    61677 => 'cloud_download',
    61678 => 'cloud_upload',
    61680 => 'user_md',
    61681 => 'stethoscope',
    61682 => 'suitcase',
    61683 => 'bell_alt',
    61684 => 'coffee',
    61685 => 'food',
    61686 => 'file_text_alt',
    61687 => 'building',
    61688 => 'hospital',
    61689 => 'ambulance',
    61690 => 'medkit',
    61691 => 'fighter_jet',
    61692 => 'beer',
    61693 => 'h_sign',
    61696 => 'double_angle_left',
    61697 => 'double_angle_right',
    61698 => 'double_angle_up',
    61699 => 'double_angle_down',
    61700 => 'angle_left',
    61701 => 'angle_right',
    61702 => 'angle_up',
    61703 => 'angle_down',
    61704 => 'desktop',
    61705 => 'laptop',
    61706 => 'tablet',
    61707 => 'mobile_phone',
    61708 => 'circle_blank',
    61709 => 'quote_left',
    61710 => 'quote_right',
    61712 => 'spinner',
    61713 => 'circle',
    61714 => 'reply',
    61715 => 'github_alt',
    61716 => 'folder_close_alt',
    61717 => 'folder_open_alt',
    61718 => 'expand_alt',
    61719 => 'collapse_alt',
    61720 => 'smile',
    61721 => 'frown',
    61722 => 'meh',
    61723 => 'gamepad',
    61724 => 'keyboard',
    61725 => 'flag_alt',
    61726 => 'flag_checkered',
    61728 => 'terminal',
    61729 => 'code',
    61730 => 'reply_all',
    61731 => 'star_half_empty',
    61732 => 'location_arrow',
    61733 => 'crop',
    61734 => 'code_fork',
    61735 => 'unlink',
    61736 => 'question',
    61737 => '_279',
    61738 => 'exclamation',
    61739 => 'superscript',
    61740 => 'subscript',
    61741 => '_283',
    61742 => 'puzzle_piece',
    61744 => 'microphone',
    61745 => 'microphone_off',
    61746 => 'shield',
    61747 => 'calendar_empty',
    61748 => 'fire_extinguisher',
    61749 => 'rocket',
    61750 => 'maxcdn',
    61751 => 'chevron_sign_left',
    61752 => 'chevron_sign_right',
    61753 => 'chevron_sign_up',
    61754 => 'chevron_sign_down',
    61755 => 'html5',
    61756 => 'css3',
    61757 => 'anchor',
    61758 => 'unlock_alt',
    61760 => 'bullseye',
    61761 => 'ellipsis_horizontal',
    61762 => 'ellipsis_vertical',
    61763 => '_303',
    61764 => 'play_sign',
    61765 => 'ticket',
    61766 => 'minus_sign_alt',
    61767 => 'check_minus',
    61768 => 'level_up',
    61769 => 'level_down',
    61770 => 'check_sign',
    61771 => 'edit_sign',
    61772 => '_312',
    61773 => 'share_sign',
    61774 => 'compass',
    61776 => 'collapse',
    61777 => 'collapse_top',
    61778 => '_317',
    61779 => 'eur',
    61780 => 'gbp',
    61781 => 'usd',
    61782 => 'inr',
    61783 => 'jpy',
    61784 => 'rub',
    61785 => 'krw',
    61786 => 'btc',
    61787 => 'file',
    61788 => 'file_text',
    61789 => 'sort_by_alphabet',
    61790 => '_329',
    61792 => 'sort_by_attributes',
    61793 => 'sort_by_attributes_alt',
    61794 => 'sort_by_order',
    61795 => 'sort_by_order_alt',
    61796 => '_334',
    61797 => '_335',
    61798 => 'youtube_sign',
    61799 => 'youtube',
    61800 => 'xing',
    61801 => 'xing_sign',
    61802 => 'youtube_play',
    61803 => 'dropbox',
    61804 => 'stackexchange',
    61805 => 'instagram',
    61806 => 'flickr',
    61808 => 'adn',
    61810 => 'bitbucket_sign',
    61811 => 'tumblr',
    61812 => 'tumblr_sign',
    61813 => 'long_arrow_down',
    61814 => 'long_arrow_up',
    61815 => 'long_arrow_left',
    61816 => 'long_arrow_right',
    61817 => 'applelogo',
    61818 => 'windows',
    61819 => 'android',
    61820 => 'linux',
    61821 => 'dribble',
    61822 => 'skype',
    61824 => 'foursquare',
    61825 => 'trello',
    61826 => 'female',
    61827 => 'male',
    61828 => 'gittip',
    61829 => 'sun',
    61830 => '_366',
    61831 => 'archive',
    61832 => 'bug',
    61833 => 'vk',
    61834 => 'weibo',
    61835 => 'renren',
    61836 => '_372',
    61837 => 'stack_exchange',
    61838 => '_374',
    61840 => 'arrow_circle_alt_left',
    61841 => '_376',
    61842 => 'dot_circle_alt',
    61843 => '_378',
    61844 => 'vimeo_square',
    61845 => '_380',
    61846 => 'plus_square_o',
    61847 => '_382',
    61848 => '_383',
    61849 => '_384',
    61850 => '_385',
    61851 => '_386',
    61852 => '_387',
    61853 => '_388',
    61854 => '_389',
    61858 => '_392',
    61859 => '_393',
    61861 => '_395',
    61862 => '_396',
    61863 => '_397',
    61864 => '_398',
    61865 => '_399',
    61866 => '_400',
    61868 => '_402',
    61869 => '_403',
    61870 => '_404',
    61872 => 'uniF1B1',
    61873 => '_406',
    61874 => '_407',
    61875 => '_408',
    61876 => '_409',
    61877 => '_410',
    61878 => '_411',
    61879 => '_412',
    61880 => '_413',
    61881 => '_414',
    61882 => '_415',
    61883 => '_416',
    61884 => '_417',
    61885 => '_418',
    61886 => '_419',
    61890 => '_422',
    61891 => '_423',
    61892 => '_424',
    61893 => '_425',
    61894 => '_426',
    61895 => '_427',
    61896 => '_428',
    61897 => '_429',
    61898 => '_430',
    61899 => '_431',
    61900 => '_432',
    61901 => '_433',
    61902 => '_434',
    61907 => '_438',
    61908 => '_439',
    61912 => '_443',
    61913 => '_444',
    61914 => '_445',
    61915 => '_446',
    61916 => '_447',
    61917 => '_448',
    61918 => '_449',
    61921 => '_451',
    61922 => '_452',
    61923 => '_453',
    61924 => '_454',
    61925 => '_455',
    61926 => '_456',
    61927 => '_457',
    61928 => '_458',
    61929 => '_459',
    61930 => '_460',
    61931 => '_461',
    61932 => '_462',
    61933 => '_463',
    61934 => '_464',
    61937 => '_466',
    61938 => '_467',
    61940 => '_469',
    61941 => '_470',
    61942 => '_471',
    61943 => '_472',
    61944 => '_473',
    61945 => '_474',
    61946 => '_475',
    61947 => '_476',
    61949 => '_478',
    61950 => '_479',
    61952 => '_480',
    61953 => '_481',
    61954 => '_482',
    61955 => '_483',
    61956 => '_484',
    61957 => '_485',
    61958 => '_486',
    61959 => '_487',
    61960 => '_488',
    61961 => '_489',
    61962 => '_490',
    61963 => '_491',
    61964 => '_492',
    61965 => '_493',
    61966 => '_494',
    61969 => '_496',
    61971 => '_498',
    61972 => '_499',
    61973 => '_500',
    61974 => '_501',
    61975 => '_502',
    61976 => '_503',
    61977 => '_504',
    61978 => '_505',
    61979 => '_506',
    61980 => '_507',
    61981 => '_508',
    61982 => '_509',
    61985 => 'venus',
    61986 => '_511',
    61987 => '_512',
    61988 => '_513',
    61989 => '_514',
    61990 => '_515',
    61991 => '_516',
    61992 => '_517',
    61993 => '_518',
    61994 => '_519',
    61995 => '_520',
    61996 => '_521',
    61997 => '_522',
    61998 => '_523',
    61999 => '_524',
    62000 => '_525',
    62001 => '_526',
    62002 => '_527',
    62003 => '_528',
    62004 => '_529',
    62005 => '_530',
    62006 => '_531',
    62007 => '_532',
    62008 => '_533',
    62009 => '_534',
    62010 => '_535',
    62011 => '_536',
    62012 => '_537',
    62013 => '_538',
    62014 => '_539',
    62016 => '_540',
    62017 => '_541',
    62018 => '_542',
    62019 => '_543',
    62020 => '_544',
    62021 => '_545',
    62022 => '_546',
    62023 => '_547',
    62024 => '_548',
    62025 => '_549',
    62026 => '_550',
    62027 => '_551',
    62028 => '_552',
    62029 => '_553',
    62030 => '_554',
    62032 => '_555',
    62033 => '_556',
    62034 => '_557',
    62035 => '_558',
    62036 => '_559',
    62037 => '_560',
    62038 => '_561',
    62039 => '_562',
    62040 => '_563',
    62041 => '_564',
    62042 => '_565',
    62043 => '_566',
    62044 => '_567',
    62045 => '_568',
    62046 => '_569',
    62050 => '_572',
    62052 => '_574',
    62053 => '_575',
    62054 => '_576',
    62055 => '_577',
    62056 => '_578',
    62057 => '_579',
    62058 => '_580',
    62059 => '_581',
    62060 => '_582',
    62061 => '_583',
    62062 => '_584',
    62064 => '_585',
    62065 => '_586',
    62066 => '_587',
    62067 => '_588',
    62068 => '_589',
    62069 => '_590',
    62070 => '_591',
    62071 => '_592',
    62072 => '_593',
    62073 => '_594',
    62074 => '_595',
    62075 => '_596',
    62076 => '_597',
    62077 => '_598',
    62082 => '_602',
    62083 => '_603',
    62084 => '_604',
    62087 => '_607',
    62088 => '_608',
    62089 => '_609',
    62090 => '_610',
    62091 => '_611',
    62092 => '_612',
    62093 => '_613',
    62094 => '_614',
    62096 => '_615',
    62097 => '_616',
    62098 => '_617',
    62099 => '_618',
    62100 => '_619',
    62101 => '_620',
    62102 => '_621',
    62103 => '_622',
    62104 => '_623',
    62105 => '_624',
    62106 => '_625',
    62107 => '_626',
    62108 => '_627',
    62109 => '_628',
    62110 => '_629',
    62720 => 'lessequal',
  ),
  'isUnicode' => true,
  'EncodingScheme' => 'FontSpecific',
  'FontName' => 'FontAwesome',
  'FullName' => 'FontAwesome Regular',
  'Version' => 'Version 4.4.1 2015',
  'PostScriptName' => 'FontAwesome',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'UnderlineThickness' => '0',
  'UnderlinePosition' => '0',
  'FontHeightOffset' => '0',
  'Ascender' => '857',
  'Descender' => '-162',
  'FontBBox' => 
  array (
    0 => '-1',
    1 => '-143',
    2 => '1286',
    3 => '857',
  ),
  'StartCharMetrics' => '647',
  'C' => 
  array (
    32 => 250.0,
    160 => 250.0,
    168 => 1000.0,
    169 => 1000.0,
    174 => 1000.0,
    180 => 1000.0,
    198 => 1000.0,
    216 => 1000.0,
    8192 => 429.0,
    8193 => 858.0,
    8194 => 429.0,
    8195 => 858.0,
    8196 => 286.0,
    8197 => 214.0,
    8198 => 143.0,
    8199 => 143.0,
    8200 => 107.0,
    8201 => 171.0,
    8202 => 47.0,
    8239 => 171.0,
    8287 => 214.0,
    8482 => 1000.0,
    8734 => 1000.0,
    8800 => 1000.0,
    9724 => 279.0,
    61440 => 1000.0,
    61441 => 857.0,
    61442 => 929.0,
    61443 => 1000.0,
    61444 => 1000.0,
    61445 => 929.0,
    61446 => 929.0,
    61447 => 786.0,
    61448 => 1071.0,
    61449 => 929.0,
    61450 => 1000.0,
    61451 => 1000.0,
    61452 => 1000.0,
    61453 => 786.0,
    61454 => 929.0,
    61456 => 929.0,
    61457 => 857.0,
    61458 => 1000.0,
    61459 => 857.0,
    61460 => 786.0,
    61461 => 929.0,
    61462 => 857.0,
    61463 => 857.0,
    61464 => 1071.0,
    61465 => 929.0,
    61466 => 857.0,
    61467 => 857.0,
    61468 => 857.0,
    61469 => 857.0,
    61470 => 857.0,
    61473 => 857.0,
    61474 => 1000.0,
    61475 => 643.0,
    61476 => 1000.0,
    61477 => 929.0,
    61478 => 429.0,
    61479 => 643.0,
    61480 => 929.0,
    61481 => 786.0,
    61482 => 1000.0,
    61483 => 857.0,
    61484 => 1071.0,
    61485 => 929.0,
    61486 => 714.0,
    61487 => 929.0,
    61488 => 1071.0,
    61489 => 929.0,
    61490 => 786.0,
    61491 => 571.0,
    61492 => 1000.0,
    61493 => 857.0,
    61494 => 1000.0,
    61495 => 1000.0,
    61496 => 1000.0,
    61497 => 1000.0,
    61498 => 1000.0,
    61499 => 1000.0,
    61500 => 1000.0,
    61501 => 1000.0,
    61502 => 1071.0,
    61504 => 857.0,
    61505 => 571.0,
    61506 => 857.0,
    61507 => 571.0,
    61508 => 1000.0,
    61509 => 929.0,
    61510 => 929.0,
    61511 => 1000.0,
    61512 => 571.0,
    61513 => 1000.0,
    61514 => 929.0,
    61515 => 786.0,
    61516 => 857.0,
    61517 => 857.0,
    61518 => 929.0,
    61520 => 1000.0,
    61521 => 571.0,
    61522 => 858.0,
    61523 => 714.0,
    61524 => 714.0,
    61525 => 857.0,
    61526 => 857.0,
    61527 => 857.0,
    61528 => 857.0,
    61529 => 857.0,
    61530 => 857.0,
    61531 => 857.0,
    61532 => 857.0,
    61533 => 857.0,
    61534 => 857.0,
    61536 => 857.0,
    61537 => 857.0,
    61538 => 929.0,
    61539 => 929.0,
    61540 => 1000.0,
    61541 => 857.0,
    61542 => 857.0,
    61543 => 786.0,
    61544 => 786.0,
    61545 => 929.0,
    61546 => 857.0,
    61547 => 857.0,
    61548 => 1000.0,
    61549 => 786.0,
    61550 => 1000.0,
    61552 => 1000.0,
    61553 => 1000.0,
    61554 => 786.0,
    61555 => 929.0,
    61556 => 1000.0,
    61557 => 1000.0,
    61558 => 857.0,
    61559 => 1000.0,
    61560 => 1000.0,
    61561 => 1071.0,
    61562 => 929.0,
    61563 => 929.0,
    61564 => 1071.0,
    61565 => 429.0,
    61566 => 1000.0,
    61568 => 1143.0,
    61569 => 857.0,
    61570 => 857.0,
    61571 => 1000.0,
    61572 => 1000.0,
    61573 => 1071.0,
    61574 => 1000.0,
    61575 => 857.0,
    61576 => 857.0,
    61577 => 500.0,
    61578 => 1000.0,
    61579 => 929.0,
    61580 => 857.0,
    61581 => 643.0,
    61582 => 1000.0,
    61584 => 857.0,
    61585 => 929.0,
    61586 => 857.0,
    61587 => 929.0,
    61588 => 857.0,
    61589 => 786.0,
    61590 => 786.0,
    61591 => 714.0,
    61592 => 857.0,
    61593 => 929.0,
    61594 => 571.0,
    61595 => 857.0,
    61596 => 929.0,
    61597 => 1071.0,
    61598 => 786.0,
    61600 => 857.0,
    61601 => 1000.0,
    61602 => 1000.0,
    61603 => 857.0,
    61604 => 1000.0,
    61605 => 1000.0,
    61606 => 857.0,
    61607 => 857.0,
    61608 => 857.0,
    61609 => 857.0,
    61610 => 857.0,
    61611 => 857.0,
    61612 => 857.0,
    61613 => 929.0,
    61614 => 1000.0,
    61616 => 786.0,
    61617 => 1000.0,
    61618 => 857.0,
    61632 => 1071.0,
    61633 => 929.0,
    61634 => 1071.0,
    61635 => 929.0,
    61636 => 1000.0,
    61637 => 1000.0,
    61638 => 786.0,
    61639 => 857.0,
    61640 => 857.0,
    61641 => 857.0,
    61642 => 1000.0,
    61643 => 1000.0,
    61644 => 1000.0,
    61645 => 857.0,
    61646 => 929.0,
    61648 => 929.0,
    61649 => 1000.0,
    61650 => 857.0,
    61651 => 857.0,
    61652 => 857.0,
    61653 => 1286.0,
    61654 => 1071.0,
    61655 => 571.0,
    61656 => 571.0,
    61657 => 357.0,
    61658 => 357.0,
    61659 => 929.0,
    61660 => 571.0,
    61661 => 571.0,
    61662 => 571.0,
    61664 => 1000.0,
    61665 => 857.0,
    61666 => 857.0,
    61667 => 1000.0,
    61668 => 1000.0,
    61669 => 1000.0,
    61670 => 1000.0,
    61671 => 500.0,
    61672 => 1000.0,
    61673 => 929.0,
    61674 => 1000.0,
    61675 => 571.0,
    61676 => 1000.0,
    61677 => 1071.0,
    61678 => 1071.0,
    61680 => 786.0,
    61681 => 786.0,
    61682 => 1000.0,
    61683 => 1000.0,
    61684 => 1071.0,
    61685 => 786.0,
    61686 => 857.0,
    61687 => 786.0,
    61688 => 786.0,
    61689 => 1071.0,
    61690 => 1000.0,
    61691 => 1071.0,
    61692 => 929.0,
    61693 => 857.0,
    61694 => 857.0,
    61696 => 571.0,
    61697 => 571.0,
    61698 => 643.0,
    61699 => 643.0,
    61700 => 357.0,
    61701 => 357.0,
    61702 => 643.0,
    61703 => 643.0,
    61704 => 1071.0,
    61705 => 1071.0,
    61706 => 643.0,
    61707 => 429.0,
    61708 => 857.0,
    61709 => 929.0,
    61710 => 929.0,
    61712 => 1000.0,
    61713 => 857.0,
    61714 => 1000.0,
    61715 => 929.0,
    61716 => 929.0,
    61717 => 1071.0,
    61718 => 1000.0,
    61719 => 1000.0,
    61720 => 857.0,
    61721 => 857.0,
    61722 => 857.0,
    61723 => 1071.0,
    61724 => 1071.0,
    61725 => 1000.0,
    61726 => 1000.0,
    61728 => 929.0,
    61729 => 1071.0,
    61730 => 1000.0,
    61731 => 929.0,
    61732 => 786.0,
    61733 => 929.0,
    61734 => 571.0,
    61735 => 929.0,
    61736 => 571.0,
    61737 => 357.0,
    61738 => 357.0,
    61739 => 857.0,
    61740 => 857.0,
    61741 => 1071.0,
    61742 => 929.0,
    61744 => 643.0,
    61745 => 786.0,
    61746 => 714.0,
    61747 => 929.0,
    61748 => 786.0,
    61749 => 929.0,
    61750 => 1000.0,
    61751 => 857.0,
    61752 => 857.0,
    61753 => 857.0,
    61754 => 857.0,
    61755 => 786.0,
    61756 => 1000.0,
    61757 => 1000.0,
    61758 => 643.0,
    61760 => 857.0,
    61761 => 786.0,
    61762 => 214.0,
    61763 => 857.0,
    61764 => 857.0,
    61765 => 1000.0,
    61766 => 857.0,
    61767 => 786.0,
    61768 => 571.0,
    61769 => 571.0,
    61770 => 857.0,
    61771 => 857.0,
    61772 => 857.0,
    61773 => 857.0,
    61774 => 857.0,
    61776 => 857.0,
    61777 => 857.0,
    61778 => 857.0,
    61779 => 571.0,
    61780 => 571.0,
    61781 => 571.0,
    61782 => 501.0,
    61783 => 573.0,
    61784 => 714.0,
    61785 => 1000.0,
    61786 => 714.0,
    61787 => 857.0,
    61788 => 857.0,
    61789 => 929.0,
    61790 => 929.0,
    61792 => 1000.0,
    61793 => 1000.0,
    61794 => 857.0,
    61795 => 857.0,
    61796 => 929.0,
    61797 => 929.0,
    61798 => 857.0,
    61799 => 857.0,
    61800 => 786.0,
    61801 => 857.0,
    61802 => 1000.0,
    61803 => 1000.0,
    61804 => 857.0,
    61805 => 857.0,
    61806 => 857.0,
    61808 => 857.0,
    61809 => 786.0,
    61810 => 857.0,
    61811 => 571.0,
    61812 => 857.0,
    61813 => 429.0,
    61814 => 429.0,
    61815 => 1000.0,
    61816 => 1000.0,
    61817 => 786.0,
    61818 => 929.0,
    61819 => 786.0,
    61820 => 857.0,
    61821 => 857.0,
    61822 => 857.0,
    61824 => 714.0,
    61825 => 857.0,
    61826 => 714.0,
    61827 => 571.0,
    61828 => 857.0,
    61829 => 1000.0,
    61830 => 857.0,
    61831 => 1000.0,
    61832 => 929.0,
    61833 => 1071.0,
    61834 => 1000.0,
    61835 => 857.0,
    61836 => 786.0,
    61837 => 714.0,
    61838 => 857.0,
    61840 => 857.0,
    61841 => 857.0,
    61842 => 857.0,
    61843 => 929.0,
    61844 => 857.0,
    61845 => 643.0,
    61846 => 786.0,
    61847 => 1214.0,
    61848 => 929.0,
    61849 => 857.0,
    61850 => 1000.0,
    61851 => 1000.0,
    61852 => 1143.0,
    61853 => 1286.0,
    61854 => 857.0,
    61856 => 857.0,
    61857 => 1000.0,
    61858 => 857.0,
    61859 => 857.0,
    61860 => 1071.0,
    61861 => 857.0,
    61862 => 1143.0,
    61863 => 857.0,
    61864 => 1137.0,
    61865 => 857.0,
    61866 => 857.0,
    61867 => 857.0,
    61868 => 1000.0,
    61869 => 857.0,
    61870 => 714.0,
    61872 => 929.0,
    61873 => 429.0,
    61874 => 1000.0,
    61875 => 1286.0,
    61876 => 1143.0,
    61877 => 857.0,
    61878 => 1000.0,
    61879 => 857.0,
    61880 => 1000.0,
    61881 => 1143.0,
    61882 => 1143.0,
    61883 => 857.0,
    61884 => 857.0,
    61885 => 571.0,
    61886 => 1286.0,
    61888 => 857.0,
    61889 => 857.0,
    61890 => 857.0,
    61891 => 857.0,
    61892 => 857.0,
    61893 => 857.0,
    61894 => 857.0,
    61895 => 857.0,
    61896 => 857.0,
    61897 => 857.0,
    61898 => 857.0,
    61899 => 1000.0,
    61900 => 1143.0,
    61901 => 1000.0,
    61902 => 1000.0,
    61904 => 1000.0,
    61905 => 1000.0,
    61906 => 857.0,
    61907 => 1000.0,
    61908 => 857.0,
    61909 => 714.0,
    61910 => 1000.0,
    61911 => 1143.0,
    61912 => 1000.0,
    61913 => 1000.0,
    61914 => 857.0,
    61915 => 857.0,
    61916 => 1000.0,
    61917 => 714.0,
    61918 => 857.0,
    61920 => 857.0,
    61921 => 857.0,
    61922 => 1000.0,
    61923 => 1000.0,
    61924 => 1000.0,
    61925 => 1000.0,
    61926 => 1000.0,
    61927 => 1000.0,
    61928 => 1000.0,
    61929 => 857.0,
    61930 => 1143.0,
    61931 => 1143.0,
    61932 => 1000.0,
    61933 => 857.0,
    61934 => 1000.0,
    61936 => 1286.0,
    61937 => 1286.0,
    61938 => 1286.0,
    61939 => 1286.0,
    61940 => 1286.0,
    61941 => 1286.0,
    61942 => 1143.0,
    61943 => 1143.0,
    61944 => 786.0,
    61945 => 857.0,
    61946 => 857.0,
    61947 => 1000.0,
    61948 => 1000.0,
    61949 => 1000.0,
    61950 => 1143.0,
    61952 => 1000.0,
    61953 => 1143.0,
    61954 => 1000.0,
    61955 => 857.0,
    61956 => 1143.0,
    61957 => 1143.0,
    61958 => 1286.0,
    61959 => 857.0,
    61960 => 1143.0,
    61961 => 714.0,
    61962 => 1143.0,
    61963 => 857.0,
    61964 => 857.0,
    61965 => 857.0,
    61966 => 1143.0,
    61968 => 857.0,
    61969 => 857.0,
    61970 => 1143.0,
    61971 => 1143.0,
    61972 => 857.0,
    61973 => 1143.0,
    61974 => 1143.0,
    61975 => 929.0,
    61976 => 929.0,
    61977 => 1143.0,
    61978 => 1143.0,
    61979 => 857.0,
    61980 => 1286.0,
    61981 => 857.0,
    61982 => 1000.0,
    61985 => 714.0,
    61986 => 857.0,
    61987 => 714.0,
    61988 => 857.0,
    61989 => 1000.0,
    61990 => 1000.0,
    61991 => 1071.0,
    61992 => 1143.0,
    61993 => 857.0,
    61994 => 714.0,
    61995 => 1143.0,
    61996 => 714.0,
    61997 => 714.0,
    61998 => 1000.0,
    61999 => 1000.0,
    62000 => 857.0,
    62001 => 714.0,
    62002 => 857.0,
    62003 => 1000.0,
    62004 => 1143.0,
    62005 => 1143.0,
    62006 => 1143.0,
    62007 => 857.0,
    62008 => 857.0,
    62009 => 857.0,
    62010 => 1000.0,
    62011 => 857.0,
    62012 => 1281.0,
    62013 => 1286.0,
    62014 => 1000.0,
    62016 => 1286.0,
    62017 => 1286.0,
    62018 => 1286.0,
    62019 => 1286.0,
    62020 => 1286.0,
    62021 => 714.0,
    62022 => 571.0,
    62023 => 1143.0,
    62024 => 1286.0,
    62025 => 857.0,
    62026 => 857.0,
    62027 => 1286.0,
    62028 => 1286.0,
    62029 => 1000.0,
    62030 => 1286.0,
    62032 => 857.0,
    62033 => 857.0,
    62034 => 857.0,
    62035 => 857.0,
    62036 => 857.0,
    62037 => 857.0,
    62038 => 1000.0,
    62039 => 1000.0,
    62040 => 1143.0,
    62041 => 1143.0,
    62042 => 1000.0,
    62043 => 857.0,
    62044 => 1101.0,
    62045 => 1000.0,
    62046 => 1000.0,
    62048 => 1143.0,
    62049 => 1000.0,
    62050 => 1286.0,
    62051 => 714.0,
    62052 => 857.0,
    62053 => 960.0,
    62054 => 1286.0,
    62055 => 1000.0,
    62056 => 1000.0,
    62057 => 1000.0,
    62058 => 1000.0,
    62059 => 1000.0,
    62060 => 1143.0,
    62061 => 1000.0,
    62062 => 857.0,
    62064 => 1000.0,
    62065 => 1000.0,
    62066 => 1000.0,
    62067 => 1000.0,
    62068 => 1000.0,
    62069 => 1000.0,
    62070 => 571.0,
    62071 => 1000.0,
    62072 => 1143.0,
    62073 => 1000.0,
    62074 => 1000.0,
    62075 => 1000.0,
    62076 => 571.0,
    62077 => 1000.0,
    62078 => 857.0,
    62080 => 857.0,
    62081 => 1000.0,
    62082 => 1000.0,
    62083 => 1286.0,
    62084 => 1000.0,
    62085 => 1000.0,
    62086 => 1000.0,
    62087 => 1286.0,
    62088 => 1000.0,
    62089 => 1286.0,
    62090 => 857.0,
    62091 => 857.0,
    62092 => 857.0,
    62093 => 857.0,
    62094 => 857.0,
    62096 => 1000.0,
    62097 => 1143.0,
    62098 => 1000.0,
    62099 => 857.0,
    62100 => 571.0,
    62101 => 857.0,
    62102 => 1000.0,
    62103 => 1000.0,
    62104 => 1000.0,
    62105 => 1000.0,
    62106 => 1000.0,
    62107 => 1000.0,
    62108 => 1000.0,
    62109 => 1000.0,
    62110 => 1000.0,
    62720 => 1000.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJzt1GPMIGcUhuHnOWvbtm3btm3bZrtb27Ztc1Fjt7Ztd7d92x9N0zTNJu32yyb39Wtm7pmTk8lkpH8p078dcIjL/JfzLMr6x3G2P13PfgCzchzAPTkP4B4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAwKEnl3Irj/Iqn/KrgAqqkAqryH82veh/NunvFTvI83Egimf0AsgQJTJ6gaRkRi8AAMAhpFRGLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPA/Ka0yKqtyKq8KqqhKqqwqqqpqqq4aqpl6LdVWHdVVPdVXAzVUIzVWEzVVMzVXi98ntFQrtVYbtVU7tVcHdVQndVYXdVU3dVcP9VQv9VYf9VU/9dcADdQgDdYQDdWw9PRwjdBIjdJojdFYjdN4TdBETdJkTdHU1KdpumZopmZptuZoruZpvhZooRZpsZakvlTLtFwrtFKrtFprtFbrtF4btFGbtDn1Ldqqw3S4tmm7jtCROkpH6xgdq+N0vE5I/USdpJN1ik7VaTpdZ+hMnaWzdY7O1Xk6P/ULdKEu0sW6RJfqMl2uK3SlrtLVukbX6rrUr9cNulE36Wbdolt1m27XHbpTd+lu3aN7U79P9+uBf3j/D+ohPawd2qld2q1H9Kge0+N6Qk/qKT2d+jN6Vnu0V8/peb2gF/WSXtYrelWv6XW9kfqbektv6x29q/f0vj7Qh/pIH+sTfarP9HnqX+hLfaWv9Y2+1Xf6Xj/oR/2kn7VP+/WLZNkOZ3JmZ3FWZ3N253BO53Ju53He1PM5vwu4oAu5sIu4qIu5uEu4pEu5tMukXtblXN4VXNGVXNlVXNXVXN01XNO1XDv1Oq7req7vBm7oRm7sJm7qZm7uFm7pVqm3dhu3dTu3dwd3dCd3dhd3dTd3dw/3TL2Xe7uP+7qf+3uAB3qQB3uIh3qYh3tE6iM9yqM9xmM9zuM9wRM9yZM9xVM9zdNTn+GZnuXZnuO5nuf5XuCFXuTFXuKlXpb6cq/wSq/yaq/xWq/zem/wRm/yZm/x1tQP8+He5u0+wkf6KB/tY3ysj/PxPsEn+qTUT/YpPtWn+XSf4TN9ls/2OT7X5/l8X+ALU7/IF/sSX+rLfLmv8JW+ylf7Gl/r63y9b0j9Rt/km32Lb/Vtvt13+E7f5bt9j+/1fb4/9Qf8oB/yw97hnd7l3X7Ej/oxP+4n/KSfSv1pP+Nnvcd7/Zyf9wt+0S/5Zb/iV/2aX0/9Db/pt/y23/G7fs/v+wN/6I/8sT/xp/4s9c/9hb/0V/7a3/hbf+fv/YN/9E/+2fu8P/VfQuGIyBSZI0tkjWyRPXJEzsgVuSPPbx9w5I18kT8KRMEoFIWjSBSNYlE8SkTJKBWlo0yUjXJRPipExagUlaNKVI1qUT1qRM2olZ6uHXWibtSL+tEgGkajaBxNomk0i+bRIlqm3ipaR5toG+2ifXSIjtEpOkeX6Brdonv0SL1n9Ire0Sf6Rr/oHwNiYAyKwTEkhsawGJ76iBgZo2J0jImxMS7Gx4SYGJNickyJqTEt9ekxI2bGrJgdc2JuzIv5sSAWxqJYHEtiaerLYnmsiJWxKlbHmlgb62J9bIiNsSk2x5aD8kcGgP9FbM3oDQAAAAAAAAAAB8OvAzAkcA==',
  '_version_' => 6,
);