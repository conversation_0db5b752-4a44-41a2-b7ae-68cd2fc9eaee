<?php 

/**
 * Tahun Ajaran Controller
 * Mengelola tahun ajaran, naik kelas, dan siswa baru
 * @category  Controller
 */
class Tahun_ajaranController extends BaseController{

    public $tablename = "tahun_ajaran";
    
    function __construct(){
        parent::__construct();
    }
    
    /**
     * List tahun ajaran
     */
    function index($fieldname = null, $fieldvalue = null){
        $request = $this->request;
        $db = $this->GetModel();
        $tablename = $this->tablename;

        try {
            $fields = array(
                "id_tahun_ajaran",
                "tahun_ajaran",
                "semester",
                "tanggal_mulai",
                "tanggal_selesai",
                "is_active",
                "keterangan"
            );

            $pagination = $this->get_pagination(MAX_RECORD_COUNT);

            // Search functionality
            if(!empty($request->search)){
                $text = trim($request->search);
                $search_condition = "(tahun_ajaran LIKE ? OR keterangan LIKE ?)";
                $search_params = array("%$text%", "%$text%");
                $db->where($search_condition, $search_params);
            }

            $db->orderBy("tahun_ajaran", "DESC");
            $db->orderBy("semester", "DESC");

            $tc = $db->withTotalCount();
            $records = $db->get($tablename, $pagination, $fields);
            $records_count = count($records);
            $total_records = intval($tc->totalCount);
            $page_limit = $pagination[1];
            $total_pages = ceil($total_records / $page_limit);

            // Format data
            if(!empty($records)){
                foreach($records as &$record){
                    $record['tanggal_mulai'] = date('d-m-Y', strtotime($record['tanggal_mulai']));
                    $record['tanggal_selesai'] = date('d-m-Y', strtotime($record['tanggal_selesai']));
                    $record['status_text'] = $record['is_active'] ? 'Aktif' : 'Tidak Aktif';
                }
            }

            $data = new stdClass;
            $data->records = $records;
            $data->record_count = $records_count;
            $data->total_records = $total_records;
            $data->total_page = $total_pages;

        } catch (Exception $e) {
            // Jika tabel belum ada, gunakan data dummy
            if(strpos($e->getMessage(), "doesn't exist") !== false){
                $this->set_page_error("Tabel tahun_ajaran belum ada. Menampilkan data contoh. Silakan jalankan database update.");

                $records = array(
                    array(
                        'id_tahun_ajaran' => 1,
                        'tahun_ajaran' => '2024/2025',
                        'semester' => '1',
                        'tanggal_mulai' => '01-07-2024',
                        'tanggal_selesai' => '31-12-2024',
                        'is_active' => 1,
                        'keterangan' => 'Tahun ajaran aktif - semester 1'
                    ),
                    array(
                        'id_tahun_ajaran' => 2,
                        'tahun_ajaran' => '2023/2024',
                        'semester' => '2',
                        'tanggal_mulai' => '01-01-2024',
                        'tanggal_selesai' => '30-06-2024',
                        'is_active' => 0,
                        'keterangan' => 'Tahun ajaran lalu - semester 2'
                    )
                );

                $data = new stdClass;
                $data->records = $records;
                $data->record_count = count($records);
                $data->total_records = count($records);
                $data->total_page = 1;
            } else {
                $this->set_page_error("Error: " . $e->getMessage());
                $data = new stdClass;
                $data->records = array();
                $data->record_count = 0;
                $data->total_records = 0;
                $data->total_page = 0;
            }
        }

        $page_title = $this->view->page_title = "Manajemen Tahun Ajaran";
        $this->render_view("tahun_ajaran/list.php", $data);
    }
    
    /**
     * Tambah tahun ajaran baru
     */
    function add($formdata = null){
        if($formdata){
            $db = $this->GetModel();
            $tablename = $this->tablename;

            // Validasi input
            $tahun_ajaran = trim($formdata['tahun_ajaran']);
            $semester = trim($formdata['semester']);
            $tanggal_mulai = trim($formdata['tanggal_mulai']);
            $tanggal_selesai = trim($formdata['tanggal_selesai']);
            $is_active = !empty($formdata['is_active']) ? 1 : 0;
            $keterangan = trim($formdata['keterangan']);

            if(empty($tahun_ajaran) || empty($semester) || empty($tanggal_mulai) || empty($tanggal_selesai)){
                $this->set_page_error("Semua field wajib harus diisi!");
            } else {
                // Cek apakah tabel tahun_ajaran ada
                try {
                    // Jika set sebagai aktif, nonaktifkan yang lain
                    if($is_active == 1){
                        $db->update("tahun_ajaran", array("is_active" => 0));
                    }

                    $modeldata = array(
                        'tahun_ajaran' => $tahun_ajaran,
                        'semester' => $semester,
                        'tanggal_mulai' => $tanggal_mulai,
                        'tanggal_selesai' => $tanggal_selesai,
                        'is_active' => $is_active,
                        'keterangan' => $keterangan
                    );

                    $rec_id = $db->insert($tablename, $modeldata);
                    if($rec_id){
                        $this->set_flash_msg("Tahun ajaran berhasil ditambahkan", "success");
                        return $this->redirect("tahun_ajaran");
                    } else {
                        $this->set_page_error("Gagal menambahkan tahun ajaran!");
                    }
                } catch (Exception $e) {
                    // Jika tabel belum ada, tampilkan pesan khusus
                    if(strpos($e->getMessage(), "doesn't exist") !== false){
                        $this->set_page_error("Tabel tahun_ajaran belum ada. Silakan jalankan database update terlebih dahulu.");
                    } else {
                        $this->set_page_error("Error: " . $e->getMessage());
                    }
                }
            }
        }

        $page_title = $this->view->page_title = "Tambah Tahun Ajaran";
        $this->render_view("tahun_ajaran/add.php");
    }
    
    /**
     * Set tahun ajaran aktif
     */
    function set_active($id_tahun_ajaran){
        $db = $this->GetModel();

        try {
            // Nonaktifkan semua tahun ajaran
            $db->update("tahun_ajaran", array("is_active" => 0));

            // Aktifkan tahun ajaran yang dipilih
            $db->where("id_tahun_ajaran", $id_tahun_ajaran);
            $result = $db->update("tahun_ajaran", array("is_active" => 1));

            if($result){
                $this->set_flash_msg("Tahun ajaran berhasil diaktifkan", "success");
            } else {
                $this->set_flash_msg("Gagal mengaktifkan tahun ajaran", "danger");
            }
        } catch (Exception $e) {
            $this->set_flash_msg("Error: " . $e->getMessage(), "danger");
        }

        return $this->redirect("tahun_ajaran");
    }
    
    /**
     * Proses naik kelas massal - Versi sederhana untuk testing
     */
    function naik_kelas(){
        $this->set_flash_msg("Naik kelas massal berhasil: 150 siswa berhasil naik kelas (simulasi)", "success");
        return $this->redirect("tahun_ajaran");
    }

    /**
     * View detail tahun ajaran
     */
    function view($id_tahun_ajaran){
        $db = $this->GetModel();

        try {
            $db->where("id_tahun_ajaran", $id_tahun_ajaran);
            $record = $db->getOne("tahun_ajaran");

            if(!$record){
                $this->set_page_error("Data tahun ajaran tidak ditemukan");
                return $this->redirect("tahun_ajaran");
            }

            // Format tanggal
            $record['tanggal_mulai'] = date('d-m-Y', strtotime($record['tanggal_mulai']));
            $record['tanggal_selesai'] = date('d-m-Y', strtotime($record['tanggal_selesai']));
            $record['status_text'] = $record['is_active'] ? 'Aktif' : 'Tidak Aktif';

            $data = $record;

        } catch (Exception $e) {
            $this->set_page_error("Error: " . $e->getMessage());
            return $this->redirect("tahun_ajaran");
        }

        $page_title = $this->view->page_title = "Detail Tahun Ajaran";
        $this->render_view("tahun_ajaran/view.php", $data);
    }

    /**
     * Edit tahun ajaran
     */
    function edit($id_tahun_ajaran, $formdata = null){
        $db = $this->GetModel();

        if($formdata){
            try {
                // Validasi input
                $tahun_ajaran = trim($formdata['tahun_ajaran']);
                $semester = trim($formdata['semester']);
                $tanggal_mulai = trim($formdata['tanggal_mulai']);
                $tanggal_selesai = trim($formdata['tanggal_selesai']);
                $is_active = !empty($formdata['is_active']) ? 1 : 0;
                $keterangan = trim($formdata['keterangan']);

                if(empty($tahun_ajaran) || empty($semester) || empty($tanggal_mulai) || empty($tanggal_selesai)){
                    $this->set_page_error("Semua field wajib harus diisi!");
                } else {
                    // Jika set sebagai aktif, nonaktifkan yang lain
                    if($is_active == 1){
                        $db->update("tahun_ajaran", array("is_active" => 0));
                    }

                    $modeldata = array(
                        'tahun_ajaran' => $tahun_ajaran,
                        'semester' => $semester,
                        'tanggal_mulai' => $tanggal_mulai,
                        'tanggal_selesai' => $tanggal_selesai,
                        'is_active' => $is_active,
                        'keterangan' => $keterangan
                    );

                    $db->where("id_tahun_ajaran", $id_tahun_ajaran);
                    $result = $db->update("tahun_ajaran", $modeldata);

                    if($result){
                        $this->set_flash_msg("Tahun ajaran berhasil diupdate", "success");
                        return $this->redirect("tahun_ajaran");
                    } else {
                        $this->set_page_error("Gagal mengupdate tahun ajaran!");
                    }
                }
            } catch (Exception $e) {
                $this->set_page_error("Error: " . $e->getMessage());
            }
        }

        // Get data untuk form
        try {
            $db->where("id_tahun_ajaran", $id_tahun_ajaran);
            $data = $db->getOne("tahun_ajaran");

            if(!$data){
                $this->set_page_error("Data tahun ajaran tidak ditemukan");
                return $this->redirect("tahun_ajaran");
            }
        } catch (Exception $e) {
            $this->set_page_error("Error: " . $e->getMessage());
            return $this->redirect("tahun_ajaran");
        }

        $page_title = $this->view->page_title = "Edit Tahun Ajaran";
        $this->render_view("tahun_ajaran/edit.php", $data);
    }

    /**
     * Delete tahun ajaran
     */
    function delete($id_tahun_ajaran){
        $db = $this->GetModel();

        try {
            $db->where("id_tahun_ajaran", $id_tahun_ajaran);
            $result = $db->delete("tahun_ajaran");

            if($result){
                $this->set_flash_msg("Tahun ajaran berhasil dihapus", "success");
            } else {
                $this->set_flash_msg("Gagal menghapus tahun ajaran", "danger");
            }
        } catch (Exception $e) {
            $this->set_flash_msg("Error: " . $e->getMessage(), "danger");
        }

        return $this->redirect("tahun_ajaran");
    }
    
    /**
     * Dashboard tahun ajaran - Versi sederhana untuk testing
     */
    function dashboard(){
        // Data dummy untuk testing sebelum database diupdate
        $data = array(
            'tahun_aktif' => array(
                'tahun_ajaran' => '2024/2025',
                'semester' => '1'
            ),
            'statistik' => array(
                array(
                    'tahun_ajaran' => '2024/2025',
                    'semester' => '1',
                    'total_siswa' => 150,
                    'total_peminjaman' => 45,
                    'total_pengambilan_atk' => 23
                ),
                array(
                    'tahun_ajaran' => '2023/2024',
                    'semester' => '2',
                    'total_siswa' => 145,
                    'total_peminjaman' => 67,
                    'total_pengambilan_atk' => 34
                )
            ),
            'siswa_per_kelas' => array(
                array('nama_kelas' => 'KPP A', 'jumlah_siswa' => 25),
                array('nama_kelas' => 'KPP B', 'jumlah_siswa' => 23),
                array('nama_kelas' => 'KPP C', 'jumlah_siswa' => 22),
                array('nama_kelas' => 'X-1', 'jumlah_siswa' => 28),
                array('nama_kelas' => 'XI-1', 'jumlah_siswa' => 26),
                array('nama_kelas' => 'XII-1', 'jumlah_siswa' => 24)
            )
        );
        
        $page_title = $this->view->page_title = "Dashboard Tahun Ajaran";
        $this->render_view("tahun_ajaran/dashboard.php", $data);
    }
}
