# 🎓 FITUR MANAJEMEN TAHUN AJARAN

## 🎯 **TUJUAN FITUR**
Memungkinkan sekolah untuk:
- ✅ **Mengelola tahun ajaran baru** tanpa kehilangan data lama
- ✅ **Naik kelas otomatis** untuk siswa lama
- ✅ **Menambah siswa baru** untuk tahun ajaran baru
- ✅ **Menyimpan arsip data** peminjaman & ATK per tahun ajaran
- ✅ **Laporan terpisah** per tahun ajaran

---

## 🗄️ **STRUKTUR DATABASE BARU**

### **1. Tabel `tahun_ajaran`**
```sql
- id_tahun_ajaran (Primary Key)
- tahun_ajaran (Format: 2024/2025)
- semester (1 atau 2)
- tanggal_mulai & tanggal_selesai
- is_active (hanya 1 yang aktif)
- keterangan
```

### **2. Tabel `riwayat_siswa`**
```sql
- id_riwayat (Primary Key)
- id_siswa (Foreign Key)
- id_tahun_ajaran (Foreign Key)
- id_kelas (Foreign Key)
- status (aktif/lulus/pindah/keluar)
- tanggal_masuk & tanggal_keluar
```

### **3. Tabel `mapping_naik_kelas`**
```sql
- id_mapping (Primary Key)
- id_kelas_asal (Foreign Key)
- id_kelas_tujuan (Foreign Key)
- keterangan
```

### **4. Update Tabel Existing**
- `siswa`: + id_tahun_ajaran_masuk, status_siswa, tanggal_masuk, tanggal_lulus
- `peminjaman`: + id_tahun_ajaran
- `pengambilan_atk`: + id_tahun_ajaran

---

## 🚀 **FITUR YANG TERSEDIA**

### **1. Dashboard Tahun Ajaran**
- 📊 **Statistik per tahun ajaran** (siswa, peminjaman, ATK)
- 📈 **Distribusi siswa per kelas** tahun aktif
- ⚡ **Quick actions** (tambah tahun ajaran, naik kelas, tambah siswa)
- 📋 **Panduan penggunaan** untuk admin

### **2. Manajemen Tahun Ajaran**
- ➕ **Tambah tahun ajaran baru**
- 🔄 **Set tahun ajaran aktif**
- 📝 **Edit & hapus tahun ajaran**
- 🔍 **Search & pagination**

### **3. Naik Kelas Otomatis**
- 🎯 **Naik kelas massal** berdasarkan mapping
- 📋 **Riwayat siswa** per tahun ajaran
- 🏆 **Status lulus** otomatis untuk siswa naik kelas
- ⚠️ **Konfirmasi** sebelum proses naik kelas

### **4. Arsip Data**
- 📚 **Data lama tetap tersimpan** dan bisa diakses
- 🔍 **Filter laporan** per tahun ajaran
- 📊 **Statistik historis** per tahun
- 💾 **Backup otomatis** sebelum naik kelas

---

## 📋 **ALUR KERJA TAHUN AJARAN BARU**

### **Langkah 1: Persiapan Tahun Ajaran Baru**
1. **Backup database** untuk keamanan
2. **Tambah tahun ajaran baru** (misal: 2025/2026)
3. **Set sebagai tahun ajaran aktif**

### **Langkah 2: Proses Naik Kelas**
1. **Verifikasi mapping naik kelas** sudah benar
2. **Jalankan naik kelas massal** dari dashboard
3. **Cek hasil** - siswa otomatis pindah ke kelas yang sesuai
4. **Siswa kelas tertinggi** otomatis berstatus "lulus"

### **Langkah 3: Tambah Siswa Baru**
1. **Tambah siswa baru** untuk kelas awal (KPP A, X-1, dll)
2. **Set tahun ajaran masuk** sesuai tahun aktif
3. **Verifikasi distribusi siswa** per kelas

### **Langkah 4: Operasional Normal**
1. **Input peminjaman & ATK** otomatis tercatat di tahun aktif
2. **Dashboard** menampilkan data tahun aktif
3. **Laporan lama** masih bisa diakses dengan filter tahun

---

## 🎯 **CONTOH SKENARIO PENGGUNAAN**

### **Skenario: Tahun Ajaran 2024/2025 → 2025/2026**

**SEBELUM (2024/2025):**
- KPP A: 25 siswa
- KPP B: 23 siswa  
- KPP C: 22 siswa
- X-1: 28 siswa
- XI-1: 26 siswa
- XII-1: 24 siswa (akan lulus)

**PROSES NAIK KELAS:**
1. XII-1 → Status "lulus" (24 siswa)
2. XI-1 → XII-1 (26 siswa)
3. X-1 → XI-1 (28 siswa)
4. KPP C → X-1 (22 siswa)
5. KPP B → KPP C (23 siswa)
6. KPP A → KPP B (25 siswa)

**SETELAH + SISWA BARU (2025/2026):**
- KPP A: 30 siswa baru
- KPP B: 25 siswa (naik dari KPP A)
- KPP C: 23 siswa (naik dari KPP B)
- X-1: 22 siswa (naik dari KPP C)
- XI-1: 28 siswa (naik dari X-1)
- XII-1: 26 siswa (naik dari XI-1)

**DATA LAMA TETAP TERSIMPAN:**
- Peminjaman tahun 2024/2025: 150 record
- Pengambilan ATK tahun 2024/2025: 89 record
- Riwayat siswa lulus: 24 siswa

---

## 🛠️ **IMPLEMENTASI**

### **1. Jalankan Database Script**
```bash
mysql -u root -p peminjaman < database_tahun_ajaran.sql
```

### **2. Akses Menu Baru**
- Menu: **🎓 Tahun Ajaran** (sudah ditambahkan)
- URL: `http://localhost/peminjaman2/tahun_ajaran/dashboard`

### **3. Setup Awal**
1. **Cek mapping naik kelas** di database (tabel `mapping_naik_kelas`)
2. **Sesuaikan dengan struktur kelas** sekolah Anda
3. **Test dengan data sample** sebelum implementasi penuh

### **4. Verifikasi Fitur**
- ✅ Dashboard tahun ajaran menampilkan statistik
- ✅ Tambah tahun ajaran baru berfungsi
- ✅ Set tahun ajaran aktif berfungsi
- ✅ Naik kelas massal berfungsi (test dengan hati-hati!)
- ✅ Data lama masih bisa diakses

---

## ⚠️ **PERINGATAN PENTING**

### **Sebelum Naik Kelas Massal:**
1. **BACKUP DATABASE** - ini wajib!
2. **Test di environment development** dulu
3. **Verifikasi mapping naik kelas** sudah benar
4. **Informasikan ke semua user** bahwa sistem sedang maintenance

### **Setelah Naik Kelas:**
1. **Cek distribusi siswa** per kelas
2. **Verifikasi siswa lulus** sudah benar
3. **Test input peminjaman/ATK** baru
4. **Backup database** hasil naik kelas

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Jika Ada Masalah:**
1. **Restore dari backup** jika naik kelas gagal
2. **Cek log error** di database
3. **Verifikasi foreign key constraints**
4. **Hubungi developer** jika perlu bantuan

### **Maintenance Rutin:**
- **Backup mingguan** database
- **Archive data** tahun ajaran lama (> 3 tahun)
- **Update mapping naik kelas** jika ada perubahan struktur
- **Monitor performa** database

---

**Status:** ✅ **SIAP UNTUK TESTING & IMPLEMENTASI**

Fitur ini memberikan solusi lengkap untuk manajemen tahun ajaran yang fleksibel dan aman! 🎉
